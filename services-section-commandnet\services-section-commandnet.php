<?php
/**
 * Plugin Name: Services Section CommandNet
 * Plugin URI: https://example.com/services-section-commandnet
 * Description: A responsive services section with hover effects and animations. Use shortcode [services_section_commandnet] to display.
 * Version: 1.0.0
 * Author: Your Name
 * Author URI: https://example.com
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: services-section-commandnet
 * Domain Path: /languages
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('SERVICES_SECTION_COMMANDNET_VERSION', '1.0.0');
define('SERVICES_SECTION_COMMANDNET_PLUGIN_URL', plugin_dir_url(__FILE__));
define('SERVICES_SECTION_COMMANDNET_PLUGIN_PATH', plugin_dir_path(__FILE__));

/**
 * Main plugin class
 */
class ServicesSection_CommandNet {
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('init', array($this, 'init'));
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
    }
    
    /**
     * Initialize the plugin
     */
    public function init() {
        // Add shortcode
        add_shortcode('services_section_commandnet', array($this, 'render_services_section'));

        // Enqueue styles and scripts
        add_action('wp_enqueue_scripts', array($this, 'enqueue_assets'));

        // Add admin notices
        add_action('admin_notices', array($this, 'admin_notices'));

        // Load text domain for translations
        load_plugin_textdomain('services-section-commandnet', false, dirname(plugin_basename(__FILE__)) . '/languages');
    }
    
    /**
     * Plugin activation
     */
    public function activate() {
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    /**
     * Enqueue CSS and JS files
     */
    public function enqueue_assets() {
        // Only enqueue if shortcode is being used on the current page
        global $post;

        // Check if we're on a page/post and the shortcode exists
        if (is_a($post, 'WP_Post') && has_shortcode($post->post_content, 'services_section_commandnet')) {
            $this->enqueue_styles();
        }

        // Also check for widgets and other content areas
        if (is_active_widget(false, false, 'text') || is_customize_preview()) {
            $this->enqueue_styles();
        }
    }

    /**
     * Enqueue the plugin styles
     */
    private function enqueue_styles() {
        wp_enqueue_style(
            'services-section-commandnet-style',
            SERVICES_SECTION_COMMANDNET_PLUGIN_URL . 'assets/css/services-section-commandnet.css',
            array(),
            SERVICES_SECTION_COMMANDNET_VERSION,
            'all'
        );
    }
    
    /**
     * Render the services section shortcode
     */
    public function render_services_section($atts) {
        // Parse shortcode attributes with defaults
        $atts = shortcode_atts(array(
            'class' => '',
            'title' => 'Services Built Specifically for your Business',
            'subtitle' => "what we're offering"
        ), $atts, 'services_section_commandnet');

        // Sanitize attributes
        $extra_class = sanitize_html_class($atts['class']);
        $title = sanitize_text_field($atts['title']);
        $subtitle = sanitize_text_field($atts['subtitle']);

        // Ensure styles are enqueued
        $this->enqueue_styles();

        // Start output buffering
        ob_start();

        // Check if template file exists
        $template_path = SERVICES_SECTION_COMMANDNET_PLUGIN_PATH . 'templates/services-section.php';
        if (file_exists($template_path)) {
            include $template_path;
        } else {
            // Fallback error message for administrators
            if (current_user_can('manage_options')) {
                echo '<div class="error"><p>' . esc_html__('Services Section CommandNet: Template file not found.', 'services-section-commandnet') . '</p></div>';
            }
        }

        // Return the buffered content
        return ob_get_clean();
    }

    /**
     * Add admin notice for missing dependencies
     */
    public function admin_notices() {
        // Check if required files exist
        $css_file = SERVICES_SECTION_COMMANDNET_PLUGIN_PATH . 'assets/css/services-section-commandnet.css';
        $template_file = SERVICES_SECTION_COMMANDNET_PLUGIN_PATH . 'templates/services-section.php';

        if (!file_exists($css_file) || !file_exists($template_file)) {
            echo '<div class="notice notice-error"><p>';
            echo esc_html__('Services Section CommandNet: Some plugin files are missing. Please reinstall the plugin.', 'services-section-commandnet');
            echo '</p></div>';
        }
    }
}

// Initialize the plugin
new ServicesSection_CommandNet();
