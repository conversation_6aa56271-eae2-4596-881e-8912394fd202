<?php
/**
 * Plugin Name: Services Section CommandNet
 * Plugin URI: https://example.com/services-section-commandnet
 * Description: A responsive services section with hover effects and animations. Use shortcode [services_section_commandnet] to display.
 * Version: 1.0.0
 * Author: Your Name
 * Author URI: https://example.com
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: services-section-commandnet
 * Domain Path: /languages
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('SERVICES_SECTION_COMMANDNET_VERSION', '1.0.0');
define('SERVICES_SECTION_COMMANDNET_PLUGIN_URL', plugin_dir_url(__FILE__));

/**
 * Main plugin class
 */
class ServicesSection_CommandNet_Simple {
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('init', array($this, 'init'));
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
    }
    
    /**
     * Initialize the plugin
     */
    public function init() {
        // Add shortcode
        add_shortcode('services_section_commandnet', array($this, 'render_services_section'));
        
        // Enqueue styles
        add_action('wp_enqueue_scripts', array($this, 'enqueue_assets'));
        add_action('wp_head', array($this, 'add_inline_styles'));
    }
    
    /**
     * Plugin activation
     */
    public function activate() {
        flush_rewrite_rules();
    }
    
    /**
     * Plugin deactivation
     */
    public function deactivate() {
        flush_rewrite_rules();
    }
    
    /**
     * Enqueue assets
     */
    public function enqueue_assets() {
        // Check if shortcode is being used
        global $post;
        if (is_a($post, 'WP_Post') && has_shortcode($post->post_content, 'services_section_commandnet')) {
            // Enqueue Google Fonts
            wp_enqueue_style(
                'services-section-commandnet-fonts',
                'https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400;1,400&display=swap',
                array(),
                SERVICES_SECTION_COMMANDNET_VERSION
            );
        }
    }
    
    /**
     * Add inline styles
     */
    public function add_inline_styles() {
        global $post;
        if (is_a($post, 'WP_Post') && has_shortcode($post->post_content, 'services_section_commandnet')) {
            echo '<style id="services-section-commandnet-styles">';
            echo $this->get_css_styles();
            echo '</style>';
        }
    }
    
    /**
     * Get CSS styles
     */
    private function get_css_styles() {
        return '
        /* Services Section CommandNet Styles */
        * { box-sizing: border-box; }
        
        .services-section-commandnet {
            min-height: 100vh;
            background-color: #111827;
            text-align: center;
            padding: 5rem 2rem;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        
        .services-subtitle-commandnet {
            color: #9ca3af;
            font-size: 1.125rem;
            max-width: 32rem;
            margin: 0 auto 0.5rem auto;
            text-transform: capitalize;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .arrow-icon-commandnet {
            color: #4f46e5;
            margin-left: 0.75rem;
            width: 1.5rem;
            height: 1.5rem;
        }
        
        .services-title-commandnet {
            color: white;
            font-size: 2.25rem;
            font-weight: 600;
            max-width: 48rem;
            margin: 0 auto 4rem auto;
            line-height: 1.375;
        }
        
        .grid-offer-commandnet {
            text-align: left;
            display: grid;
            gap: 1.25rem;
            max-width: 80rem;
            margin: 0 auto;
            grid-template-columns: 1fr;
        }
        
        .card-commandnet {
            background-color: #1f2937;
            padding: 2.5rem;
            position: relative;
        }
        
        .card-content-commandnet {
            position: relative;
            z-index: 1;
        }
        
        .card-content-right-commandnet { padding-right: 0; }
        .card-content-left-commandnet { padding-left: 0; }
        
        .card-title-commandnet {
            text-transform: capitalize;
            color: white;
            margin-bottom: 1rem;
            font-size: 1.5rem;
            font-family: "Playfair Display", serif;
            font-weight: 400;
        }
        
        .card-description-commandnet {
            color: #9ca3af;
            transition: 0.8s;
        }
        
        @media (min-width: 640px) {
            .grid-offer-commandnet { grid-template-columns: repeat(2, 1fr); }
        }
        
        @media (min-width: 768px) {
            .services-title-commandnet { font-size: 3rem; }
        }
        
        @media (min-width: 1024px) {
            .card-content-right-commandnet { padding-right: 13rem; }
            .card-content-left-commandnet { padding-left: 12rem; }
            .card-commandnet:nth-child(3) .card-content-right-commandnet { padding-right: 11rem; }
        }
        
        @media (min-width: 1280px) {
            .services-section-commandnet { padding-left: 0; padding-right: 0; }
            .services-title-commandnet { font-size: 3.75rem; }
            .card-title-commandnet { font-size: 1.875rem; }
        }
        
        .card-commandnet::before {
            position: absolute;
            content: "";
            width: 100%;
            height: 100%;
            transition: 0.6s;
            z-index: 0;
            background-color: #4f46e5;
        }
        
        .card-commandnet:hover {
            box-shadow: 0.063rem 0.063rem 1.25rem 0.375rem rgba(0, 0, 0, 0.53);
        }
        
        .card-commandnet:nth-child(1)::before {
            bottom: 0; right: 0;
            clip-path: circle(calc(6.25rem + 7.5vw) at 100% 100%);
        }
        
        .card-commandnet:nth-child(2)::before {
            bottom: 0; left: 0;
            clip-path: circle(calc(6.25rem + 7.5vw) at 0% 100%);
        }
        
        .card-commandnet:nth-child(3)::before {
            top: 0; right: 0;
            clip-path: circle(calc(6.25rem + 7.5vw) at 100% 0%);
        }
        
        .card-commandnet:nth-child(4)::before {
            top: 0; left: 0;
            clip-path: circle(calc(6.25rem + 7.5vw) at 0% 0%);
        }
        
        .card-commandnet:hover::before {
            clip-path: circle(110vw at 100% 100%);
        }
        
        .card-commandnet:hover .card-description-commandnet {
            color: #fff;
        }
        
        @media screen and (min-width: 62.5rem) {
            .circle-commandnet {
                position: absolute;
                width: 100%;
                height: 100%;
                z-index: 0;
            }
        }
        
        .card-commandnet:nth-child(1) .circle-commandnet {
            background: url("https://images.unsplash.com/photo-1587440871875-191322ee64b0?q=80&w=2071&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D") no-repeat 50% 50%/cover;
            bottom: 0; right: 0;
            clip-path: circle(calc(6.25rem + 7.5vw) at 100% 100%);
        }
        
        .card-commandnet:nth-child(2) .circle-commandnet {
            background: url("https://images.unsplash.com/photo-1499951360447-b19be8fe80f5?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D") no-repeat 50% 50%/cover;
            bottom: 0; left: 0;
            clip-path: circle(calc(6.25rem + 7.5vw) at 0% 100%);
        }
        
        .card-commandnet:nth-child(3) .circle-commandnet {
            background: url("https://images.unsplash.com/photo-1557804506-669a67965ba0?q=80&w=1974&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D") no-repeat 50% 50%/cover;
            top: 0; right: 0;
            clip-path: circle(calc(6.25rem + 7.5vw) at 100% 0%);
        }
        
        .card-commandnet:nth-child(4) .circle-commandnet {
            background: url("https://images.unsplash.com/photo-1600880292203-757bb62b4baf?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D") no-repeat 50% 50%/cover;
            top: 0; left: 0;
            clip-path: circle(calc(6.25rem + 7.5vw) at 0% 0%);
        }';
    }
    
    /**
     * Render the services section shortcode
     */
    public function render_services_section($atts) {
        $atts = shortcode_atts(array(
            'class' => '',
            'title' => 'Services Built Specifically for your Business',
            'subtitle' => "what we're offering"
        ), $atts, 'services_section_commandnet');
        
        $extra_class = sanitize_html_class($atts['class']);
        $title = sanitize_text_field($atts['title']);
        $subtitle = sanitize_text_field($atts['subtitle']);
        
        ob_start();
        ?>
        <section class="services-section-commandnet <?php echo esc_attr($extra_class); ?>">
            <span class="services-subtitle-commandnet">
                <?php echo esc_html($subtitle); ?>
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="arrow-icon-commandnet">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M17.25 8.25 21 12m0 0-3.75 3.75M21 12H3" />
                </svg>
            </span>
            <h1 class="services-title-commandnet"><?php echo esc_html($title); ?></h1>
            <div class="grid-offer-commandnet">
                <div class="card-commandnet">
                    <div class="circle-commandnet"></div>
                    <div class="card-content-commandnet card-content-right-commandnet">
                        <h2 class="card-title-commandnet">UI/UX<br />creative design</h2>
                        <p class="card-description-commandnet">Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Pellentesque habitant morbi tristique senectus et netus et malesuada fames.</p>
                    </div>
                </div>
                <div class="card-commandnet">
                    <div class="circle-commandnet"></div>
                    <div class="card-content-commandnet card-content-left-commandnet">
                        <h2 class="card-title-commandnet">visual<br />graphic design</h2>
                        <p class="card-description-commandnet">Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Pellentesque habitant morbi tristique senectus et netus et malesuada fames.</p>
                    </div>
                </div>
                <div class="card-commandnet">
                    <div class="circle-commandnet"></div>
                    <div class="card-content-commandnet card-content-right-commandnet">
                        <h2 class="card-title-commandnet">strategy &<br />digital marketing</h2>
                        <p class="card-description-commandnet">Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Pellentesque habitant morbi tristique senectus et netus et malesuada fames.</p>
                    </div>
                </div>
                <div class="card-commandnet">
                    <div class="circle-commandnet"></div>
                    <div class="card-content-commandnet card-content-left-commandnet">
                        <h2 class="card-title-commandnet">effective<br />business growth</h2>
                        <p class="card-description-commandnet">Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Pellentesque habitant morbi tristique senectus et netus et malesuada fames.</p>
                    </div>
                </div>
            </div>
        </section>
        <?php
        return ob_get_clean();
    }
}

// Initialize the plugin
new ServicesSection_CommandNet_Simple();
