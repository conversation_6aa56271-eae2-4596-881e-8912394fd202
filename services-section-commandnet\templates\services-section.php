<?php
/**
 * Services Section Template
 * 
 * This template is used by the [services_section_commandnet] shortcode
 * 
 * @package ServicesSection_CommandNet
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}
?>

<section class="services-section-commandnet <?php echo esc_attr($extra_class); ?>">
  <span class="services-subtitle-commandnet">
    <?php echo esc_html($subtitle); ?>
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="arrow-icon-commandnet">
      <path stroke-linecap="round" stroke-linejoin="round" d="M17.25 8.25 21 12m0 0-3.75 3.75M21 12H3" />
    </svg>
  </span>

  <h1 class="services-title-commandnet">
    <?php echo esc_html($title); ?>
  </h1>
  
  <div class="grid-offer-commandnet">
    <div class="card-commandnet">
      <div class="circle-commandnet"></div>
      <div class="card-content-commandnet card-content-right-commandnet">
        <h2 class="card-title-commandnet">
          <?php esc_html_e('UI/UX', 'services-section-commandnet'); ?><br />
          <?php esc_html_e('creative design', 'services-section-commandnet'); ?>
        </h2>
        <p class="card-description-commandnet">
          <?php esc_html_e('Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Pellentesque habitant morbi tristique senectus et netus et malesuada fames.', 'services-section-commandnet'); ?>
        </p>
      </div>
      <div class="icon-commandnet"></div>
    </div>
    
    <div class="card-commandnet">
      <div class="circle-commandnet"></div>
      <div class="card-content-commandnet card-content-left-commandnet">
        <h2 class="card-title-commandnet">
          <?php esc_html_e('visual', 'services-section-commandnet'); ?><br />
          <?php esc_html_e('graphic design', 'services-section-commandnet'); ?>
        </h2>
        <p class="card-description-commandnet">
          <?php esc_html_e('Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Pellentesque habitant morbi tristique senectus et netus et malesuada fames.', 'services-section-commandnet'); ?>
        </p>
      </div>
    </div>
    
    <div class="card-commandnet">
      <div class="circle-commandnet"></div>
      <div class="card-content-commandnet card-content-right-commandnet">
        <h2 class="card-title-commandnet">
          <?php esc_html_e('strategy &', 'services-section-commandnet'); ?><br />
          <?php esc_html_e('digital marketing', 'services-section-commandnet'); ?>
        </h2>
        <p class="card-description-commandnet">
          <?php esc_html_e('Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Pellentesque habitant morbi tristique senectus et netus et malesuada fames.', 'services-section-commandnet'); ?>
        </p>
      </div>
    </div>
    
    <div class="card-commandnet">
      <div class="circle-commandnet"></div>
      <div class="card-content-commandnet card-content-left-commandnet">
        <h2 class="card-title-commandnet">
          <?php esc_html_e('effective', 'services-section-commandnet'); ?><br />
          <?php esc_html_e('business growth', 'services-section-commandnet'); ?>
        </h2>
        <p class="card-description-commandnet">
          <?php esc_html_e('Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Pellentesque habitant morbi tristique senectus et netus et malesuada fames.', 'services-section-commandnet'); ?>
        </p>
      </div>
    </div>
  </div>
</section>
