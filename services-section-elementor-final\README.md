# Services Section for Elementor

A custom Elementor widget for creating responsive services sections with hover effects. Compatible with TheGem theme.

## Features

- **Responsive Design**: Adapts to all screen sizes
- **Hover Effects**: Beautiful clip-path animations on card hover
- **Customizable**: Full control over colors, images, and content
- **TheGem Compatible**: Optimized for TheGem theme
- **Easy to Use**: Simple Elementor widget interface

## Installation

1. Upload the plugin files to the `/wp-content/plugins/services-section-elementor/` directory
2. Activate the plugin through the 'Plugins' menu in WordPress
3. Make sure Elementor is installed and activated
4. Find the "Services Section" widget in Elementor's widget panel

## Usage

1. Edit any page with Elementor
2. Search for "Services Section" in the widget panel
3. Drag and drop the widget to your desired location
4. Customize the content, images, and colors in the widget settings
5. Preview and publish your page

## Customization

The widget provides extensive customization options:

- **Main Content**: Edit subtitle and main title
- **Service Cards**: Configure 4 service cards with images, titles, and descriptions
- **Colors**: Customize all colors including background, text, and hover effects

## Requirements

- WordPress 5.0 or higher
- Elementor 3.0.0 or higher
- PHP 7.4 or higher

## Support

For support and questions, please contact CommandNet Solutions.

## License

This plugin is licensed under the GPL v2 or later.
