=== Services Section for Elementor ===
Contributors: yourname
Tags: elementor, services, section, responsive, hover effects, thegem
Requires at least: 5.0
Tested up to: 6.4
Requires PHP: 7.4
Stable tag: 1.0.0
License: GPLv2 or later
License URI: https://www.gnu.org/licenses/gpl-2.0.html
Elementor tested up to: 3.25.0
Elementor Pro tested up to: 3.25.0

Custom Elementor widget for responsive services section with hover effects and animations. Fully compatible with TheGem theme.

== Description ==

Services Section for Elementor is a powerful WordPress plugin that adds a custom Elementor widget for creating stunning services sections with hover effects and animations.

**Key Features:**

* **Custom Elementor Widget** - Seamlessly integrates with Elementor page builder
* **TheGem Theme Compatible** - Specifically optimized for TheGem theme
* **Fully Customizable** - Control every aspect through Elementor's interface
* **Image Controls** - Easy background image management for each service card
* **Text Controls** - Editable titles and descriptions for all content
* **Live Preview** - See changes instantly in Elementor editor
* **Responsive Design** - Works perfectly on all devices
* **Hover Effects** - Beautiful clip-path animations on hover
* **Professional Styling** - Modern dark theme with elegant typography

**What You Can Customize:**

* Main title and subtitle
* Background images for each of the 4 service cards
* Service titles and descriptions
* Colors (background, text, hover overlay)
* Typography settings
* Spacing and padding
* All styling options through Elementor's interface

**Perfect For:**

* Business websites
* Service providers
* Agencies
* Portfolios
* Corporate sites
* Any site using TheGem theme + Elementor

== Installation ==

**Requirements:**
* WordPress 5.0 or higher
* PHP 7.4 or higher
* Elementor (free version) installed and activated

**Installation Steps:**

1. Upload the plugin files to `/wp-content/plugins/services-section-elementor/` directory
2. Activate the plugin through the 'Plugins' screen in WordPress
3. Edit any page with Elementor
4. Search for "Services Section" in the Elementor widgets panel
5. Drag and drop the widget to your page
6. Customize all settings in the left panel
7. Publish your page

== Usage ==

1. **Add the Widget**: In Elementor editor, search for "Services Section" and drag it to your page
2. **Customize Content**: Use the Content tab to edit titles, descriptions, and upload images
3. **Style Your Section**: Use the Style tab to customize colors, typography, and spacing
4. **Preview Changes**: All changes show instantly in the editor
5. **Publish**: Save and publish your page

== Frequently Asked Questions ==

= Does this work with any theme? =

While it works with most themes, it's specifically optimized for TheGem theme with special compatibility CSS included.

= Can I change the background images? =

Yes! Each service card has its own image control in the Elementor editor. Simply click and upload your own images.

= Is it responsive? =

Absolutely! The widget is fully responsive and looks great on all devices.

= Do I need Elementor Pro? =

No, this widget works with the free version of Elementor.

= Can I customize the colors? =

Yes, you have full control over all colors including background, text, and hover overlay colors.

== Screenshots ==

1. Services section on frontend with hover effects
2. Elementor editor with widget controls
3. Image upload interface
4. Style customization options
5. Responsive design on mobile

== Changelog ==

= 1.0.0 =
* Initial release
* Custom Elementor widget implementation
* TheGem theme compatibility
* Full customization controls
* Responsive design
* Hover animations
* Live preview support

== Upgrade Notice ==

= 1.0.0 =
Initial release of Services Section for Elementor plugin.
