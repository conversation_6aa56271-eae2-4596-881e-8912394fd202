<?php
/**
 * Services Section Elementor Widget
 * 
 * @package Services_Section_Elementor
 * @version 1.0.0
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

/**
 * Services Section Widget Class
 */
class Services_Section_Widget extends \Elementor\Widget_Base {

    /**
     * Get widget name.
     */
    public function get_name() {
        return 'services-section-commandnet';
    }

    /**
     * Get widget title.
     */
    public function get_title() {
        return esc_html__('Services Section', 'services-section-elementor');
    }

    /**
     * Get widget icon.
     */
    public function get_icon() {
        return 'eicon-gallery-grid';
    }

    /**
     * Get widget categories.
     */
    public function get_categories() {
        return ['general'];
    }

    /**
     * Get widget keywords.
     */
    public function get_keywords() {
        return ['services', 'section', 'cards', 'hover', 'business'];
    }

    /**
     * Register widget controls.
     */
    protected function register_controls() {

        // Content Tab - Main Settings
        $this->start_controls_section(
            'content_section',
            [
                'label' => esc_html__('Main Content', 'services-section-elementor'),
                'tab' => \Elementor\Controls_Manager::TAB_CONTENT,
            ]
        );

        $this->add_control(
            'subtitle',
            [
                'label' => esc_html__('Subtitle', 'services-section-elementor'),
                'type' => \Elementor\Controls_Manager::TEXT,
                'default' => esc_html__("what we're offering", 'services-section-elementor'),
                'placeholder' => esc_html__('Enter subtitle', 'services-section-elementor'),
            ]
        );

        $this->add_control(
            'title',
            [
                'label' => esc_html__('Main Title', 'services-section-elementor'),
                'type' => \Elementor\Controls_Manager::TEXTAREA,
                'default' => esc_html__('Services Built Specifically for your Business', 'services-section-elementor'),
                'placeholder' => esc_html__('Enter main title', 'services-section-elementor'),
                'rows' => 3,
            ]
        );

        $this->end_controls_section();

        // Content Tab - Service Cards
        $this->start_controls_section(
            'services_section',
            [
                'label' => esc_html__('Service Cards', 'services-section-elementor'),
                'tab' => \Elementor\Controls_Manager::TAB_CONTENT,
            ]
        );

        // Service 1
        $this->add_control(
            'service_1_heading',
            [
                'label' => esc_html__('Service 1', 'services-section-elementor'),
                'type' => \Elementor\Controls_Manager::HEADING,
                'separator' => 'before',
            ]
        );

        $this->add_control(
            'service_1_image',
            [
                'label' => esc_html__('Background Image', 'services-section-elementor'),
                'type' => \Elementor\Controls_Manager::MEDIA,
                'default' => [
                    'url' => 'https://images.unsplash.com/photo-1587440871875-191322ee64b0?q=80&w=2071&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
                ],
            ]
        );

        $this->add_control(
            'service_1_title',
            [
                'label' => esc_html__('Title', 'services-section-elementor'),
                'type' => \Elementor\Controls_Manager::TEXTAREA,
                'default' => esc_html__("UI/UX\ncreative design", 'services-section-elementor'),
                'rows' => 2,
            ]
        );

        $this->add_control(
            'service_1_description',
            [
                'label' => esc_html__('Description', 'services-section-elementor'),
                'type' => \Elementor\Controls_Manager::TEXTAREA,
                'default' => esc_html__('Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Pellentesque habitant morbi tristique senectus et netus et malesuada fames.', 'services-section-elementor'),
                'rows' => 3,
            ]
        );

        // Service 2
        $this->add_control(
            'service_2_heading',
            [
                'label' => esc_html__('Service 2', 'services-section-elementor'),
                'type' => \Elementor\Controls_Manager::HEADING,
                'separator' => 'before',
            ]
        );

        $this->add_control(
            'service_2_image',
            [
                'label' => esc_html__('Background Image', 'services-section-elementor'),
                'type' => \Elementor\Controls_Manager::MEDIA,
                'default' => [
                    'url' => 'https://images.unsplash.com/photo-1499951360447-b19be8fe80f5?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
                ],
            ]
        );

        $this->add_control(
            'service_2_title',
            [
                'label' => esc_html__('Title', 'services-section-elementor'),
                'type' => \Elementor\Controls_Manager::TEXTAREA,
                'default' => esc_html__("visual\ngraphic design", 'services-section-elementor'),
                'rows' => 2,
            ]
        );

        $this->add_control(
            'service_2_description',
            [
                'label' => esc_html__('Description', 'services-section-elementor'),
                'type' => \Elementor\Controls_Manager::TEXTAREA,
                'default' => esc_html__('Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Pellentesque habitant morbi tristique senectus et netus et malesuada fames.', 'services-section-elementor'),
                'rows' => 3,
            ]
        );

        // Service 3
        $this->add_control(
            'service_3_heading',
            [
                'label' => esc_html__('Service 3', 'services-section-elementor'),
                'type' => \Elementor\Controls_Manager::HEADING,
                'separator' => 'before',
            ]
        );

        $this->add_control(
            'service_3_image',
            [
                'label' => esc_html__('Background Image', 'services-section-elementor'),
                'type' => \Elementor\Controls_Manager::MEDIA,
                'default' => [
                    'url' => 'https://images.unsplash.com/photo-1557804506-669a67965ba0?q=80&w=1974&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
                ],
            ]
        );

        $this->add_control(
            'service_3_title',
            [
                'label' => esc_html__('Title', 'services-section-elementor'),
                'type' => \Elementor\Controls_Manager::TEXTAREA,
                'default' => esc_html__("strategy &\ndigital marketing", 'services-section-elementor'),
                'rows' => 2,
            ]
        );

        $this->add_control(
            'service_3_description',
            [
                'label' => esc_html__('Description', 'services-section-elementor'),
                'type' => \Elementor\Controls_Manager::TEXTAREA,
                'default' => esc_html__('Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Pellentesque habitant morbi tristique senectus et netus et malesuada fames.', 'services-section-elementor'),
                'rows' => 3,
            ]
        );

        // Service 4
        $this->add_control(
            'service_4_heading',
            [
                'label' => esc_html__('Service 4', 'services-section-elementor'),
                'type' => \Elementor\Controls_Manager::HEADING,
                'separator' => 'before',
            ]
        );

        $this->add_control(
            'service_4_image',
            [
                'label' => esc_html__('Background Image', 'services-section-elementor'),
                'type' => \Elementor\Controls_Manager::MEDIA,
                'default' => [
                    'url' => 'https://images.unsplash.com/photo-1600880292203-757bb62b4baf?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
                ],
            ]
        );

        $this->add_control(
            'service_4_title',
            [
                'label' => esc_html__('Title', 'services-section-elementor'),
                'type' => \Elementor\Controls_Manager::TEXTAREA,
                'default' => esc_html__("effective\nbusiness growth", 'services-section-elementor'),
                'rows' => 2,
            ]
        );

        $this->add_control(
            'service_4_description',
            [
                'label' => esc_html__('Description', 'services-section-elementor'),
                'type' => \Elementor\Controls_Manager::TEXTAREA,
                'default' => esc_html__('Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Pellentesque habitant morbi tristique senectus et netus et malesuada fames.', 'services-section-elementor'),
                'rows' => 3,
            ]
        );

        $this->end_controls_section();

        // Style Tab - General Styling
        $this->start_controls_section(
            'style_general',
            [
                'label' => esc_html__('General', 'services-section-elementor'),
                'tab' => \Elementor\Controls_Manager::TAB_STYLE,
            ]
        );

        $this->add_control(
            'section_background',
            [
                'label' => esc_html__('Section Background', 'services-section-elementor'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#111827',
                'selectors' => [
                    '{{WRAPPER}} .services-section-commandnet' => 'background-color: {{VALUE}};',
                ],
            ]
        );

        $this->add_responsive_control(
            'section_padding',
            [
                'label' => esc_html__('Section Padding', 'services-section-elementor'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', 'em', '%'],
                'default' => [
                    'top' => '80',
                    'right' => '32',
                    'bottom' => '80',
                    'left' => '32',
                    'unit' => 'px',
                ],
                'selectors' => [
                    '{{WRAPPER}} .services-section-commandnet' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );

        $this->end_controls_section();

        // Style Tab - Typography
        $this->start_controls_section(
            'style_typography',
            [
                'label' => esc_html__('Typography', 'services-section-elementor'),
                'tab' => \Elementor\Controls_Manager::TAB_STYLE,
            ]
        );

        $this->add_control(
            'subtitle_color',
            [
                'label' => esc_html__('Subtitle Color', 'services-section-elementor'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#9ca3af',
                'selectors' => [
                    '{{WRAPPER}} .services-subtitle-commandnet' => 'color: {{VALUE}};',
                ],
            ]
        );

        $this->add_group_control(
            \Elementor\Group_Control_Typography::get_type(),
            [
                'name' => 'subtitle_typography',
                'label' => esc_html__('Subtitle Typography', 'services-section-elementor'),
                'selector' => '{{WRAPPER}} .services-subtitle-commandnet',
            ]
        );

        $this->add_control(
            'title_color',
            [
                'label' => esc_html__('Main Title Color', 'services-section-elementor'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#ffffff',
                'selectors' => [
                    '{{WRAPPER}} .services-title-commandnet' => 'color: {{VALUE}};',
                ],
            ]
        );

        $this->add_group_control(
            \Elementor\Group_Control_Typography::get_type(),
            [
                'name' => 'title_typography',
                'label' => esc_html__('Main Title Typography', 'services-section-elementor'),
                'selector' => '{{WRAPPER}} .services-title-commandnet',
            ]
        );

        $this->end_controls_section();

        // Style Tab - Cards
        $this->start_controls_section(
            'style_cards',
            [
                'label' => esc_html__('Service Cards', 'services-section-elementor'),
                'tab' => \Elementor\Controls_Manager::TAB_STYLE,
            ]
        );

        $this->add_control(
            'card_background',
            [
                'label' => esc_html__('Card Background', 'services-section-elementor'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#1f2937',
                'selectors' => [
                    '{{WRAPPER}} .card-commandnet' => 'background-color: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'card_title_color',
            [
                'label' => esc_html__('Card Title Color', 'services-section-elementor'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#ffffff',
                'selectors' => [
                    '{{WRAPPER}} .card-title-commandnet' => 'color: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'card_description_color',
            [
                'label' => esc_html__('Card Description Color', 'services-section-elementor'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#9ca3af',
                'selectors' => [
                    '{{WRAPPER}} .card-description-commandnet' => 'color: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'hover_overlay_color',
            [
                'label' => esc_html__('Hover Overlay Color', 'services-section-elementor'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#4f46e5',
                'selectors' => [
                    '{{WRAPPER}} .card-commandnet::before' => 'background-color: {{VALUE}};',
                ],
            ]
        );

        $this->end_controls_section();
    }

    /**
     * Render widget output on the frontend.
     */
    protected function render() {
        $settings = $this->get_settings_for_display();

        // Prepare service data
        $services = [
            [
                'image' => $settings['service_1_image']['url'] ?? '',
                'title' => $settings['service_1_title'] ?? '',
                'description' => $settings['service_1_description'] ?? '',
                'position' => 'right'
            ],
            [
                'image' => $settings['service_2_image']['url'] ?? '',
                'title' => $settings['service_2_title'] ?? '',
                'description' => $settings['service_2_description'] ?? '',
                'position' => 'left'
            ],
            [
                'image' => $settings['service_3_image']['url'] ?? '',
                'title' => $settings['service_3_title'] ?? '',
                'description' => $settings['service_3_description'] ?? '',
                'position' => 'right'
            ],
            [
                'image' => $settings['service_4_image']['url'] ?? '',
                'title' => $settings['service_4_title'] ?? '',
                'description' => $settings['service_4_description'] ?? '',
                'position' => 'left'
            ]
        ];
        ?>
        <section class="services-section-commandnet">
            <span class="services-subtitle-commandnet">
                <?php echo esc_html($settings['subtitle']); ?>
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="arrow-icon-commandnet">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M17.25 8.25 21 12m0 0-3.75 3.75M21 12H3" />
                </svg>
            </span>

            <h1 class="services-title-commandnet">
                <?php echo wp_kses_post(nl2br($settings['title'])); ?>
            </h1>

            <div class="grid-offer-commandnet">
                <?php foreach ($services as $index => $service) : ?>
                    <div class="card-commandnet" <?php if ($service['image']) echo 'data-bg-image="' . esc_url($service['image']) . '"'; ?>>
                        <div class="circle-commandnet" <?php if ($service['image']) echo 'style="background-image: url(' . esc_url($service['image']) . ');"'; ?>></div>
                        <div class="card-content-commandnet card-content-<?php echo esc_attr($service['position']); ?>-commandnet">
                            <h2 class="card-title-commandnet">
                                <?php echo wp_kses_post(nl2br($service['title'])); ?>
                            </h2>
                            <p class="card-description-commandnet">
                                <?php echo esc_html($service['description']); ?>
                            </p>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </section>
        <?php
    }

    /**
     * Render widget output in the editor.
     */
    protected function content_template() {
        ?>
        <#
        var services = [
            {
                image: settings.service_1_image.url || '',
                title: settings.service_1_title || '',
                description: settings.service_1_description || '',
                position: 'right'
            },
            {
                image: settings.service_2_image.url || '',
                title: settings.service_2_title || '',
                description: settings.service_2_description || '',
                position: 'left'
            },
            {
                image: settings.service_3_image.url || '',
                title: settings.service_3_title || '',
                description: settings.service_3_description || '',
                position: 'right'
            },
            {
                image: settings.service_4_image.url || '',
                title: settings.service_4_title || '',
                description: settings.service_4_description || '',
                position: 'left'
            }
        ];
        #>
        <section class="services-section-commandnet">
            <span class="services-subtitle-commandnet">
                {{{ settings.subtitle }}}
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="arrow-icon-commandnet">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M17.25 8.25 21 12m0 0-3.75 3.75M21 12H3" />
                </svg>
            </span>

            <h1 class="services-title-commandnet">
                {{{ settings.title.replace(/\n/g, '<br>') }}}
            </h1>

            <div class="grid-offer-commandnet">
                <# _.each(services, function(service, index) { #>
                    <div class="card-commandnet" <# if (service.image) { #>data-bg-image="{{{ service.image }}}"<# } #>>
                        <div class="circle-commandnet" <# if (service.image) { #>style="background-image: url({{{ service.image }}});"<# } #>></div>
                        <div class="card-content-commandnet card-content-{{{ service.position }}}-commandnet">
                            <h2 class="card-title-commandnet">
                                {{{ service.title.replace(/\n/g, '<br>') }}}
                            </h2>
                            <p class="card-description-commandnet">
                                {{{ service.description }}}
                            </p>
                        </div>
                    </div>
                <# }); #>
            </div>
        </section>
        <?php
    }
}
