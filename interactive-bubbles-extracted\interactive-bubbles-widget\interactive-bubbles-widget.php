<?php
/**
 * Plugin Name: Interactive Bubbles Widget
 * Plugin URI: https://commandnet.co
 * Description: Gooey bubbles background with mouse‑follow effect, full color/text control + layout & typography sliders/dropdowns.
 * Version: 1.4
 * Author: CommandNet Solutions
 * Author URI: https://commandnet.co
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: interactive-bubbles-widget
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * Network: false
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Define plugin constants
define( 'IBW_VERSION', '1.4' );
define( 'IBW_PLUGIN_URL', plugin_dir_url( __FILE__ ) );
define( 'IBW_PLUGIN_PATH', plugin_dir_path( __FILE__ ) );


function ibw_hex_to_rgb( $hex ) {
    $hex = ltrim( $hex, '#' );
    if ( strlen( $hex ) === 3 ) {
        $hex = $hex[0].$hex[0].$hex[1].$hex[1].$hex[2].$hex[2];
    }
    $int = hexdec( $hex );
    return sprintf(
        '%d, %d, %d',
        ($int >> 16) & 255,
        ($int >> 8) & 255,
        $int & 255
    );
}

/**
 * Check if widget is active on current page
 */
function ibw_is_widget_active() {
    // Check if widget is active in any sidebar
    $sidebars = wp_get_sidebars_widgets();

    foreach ( $sidebars as $sidebar => $widgets ) {
        if ( is_array( $widgets ) ) {
            foreach ( $widgets as $widget ) {
                if ( strpos( $widget, 'interactive_bubbles_widget' ) === 0 ) {
                    return true;
                }
            }
        }
    }

    return false;
}

/**
 * Enqueue plugin assets
 */
function ibw_enqueue_assets() {
    // Only load assets if widget is active (optional optimization)
    // Comment out the next 3 lines if you want assets to always load
    if ( ! ibw_is_widget_active() ) {
        return;
    }
    // CSS file path
    $css_file = IBW_PLUGIN_PATH . 'interactive-bubbles-widget.css';
    $css_url = IBW_PLUGIN_URL . 'interactive-bubbles-widget.css';

    // JavaScript file path
    $js_file = IBW_PLUGIN_PATH . 'interactive-bubbles-widget.js';
    $js_url = IBW_PLUGIN_URL . 'interactive-bubbles-widget.js';

    // Debug information (remove in production)
    if ( defined( 'WP_DEBUG' ) && WP_DEBUG ) {
        error_log( 'IBW Plugin Path: ' . IBW_PLUGIN_PATH );
        error_log( 'IBW Plugin URL: ' . IBW_PLUGIN_URL );
        error_log( 'CSS file exists: ' . ( file_exists( $css_file ) ? 'Yes' : 'No' ) );
        error_log( 'JS file exists: ' . ( file_exists( $js_file ) ? 'Yes' : 'No' ) );
    }

    // Enqueue CSS
    if ( file_exists( $css_file ) ) {
        wp_enqueue_style(
            'ibw-styles',
            $css_url,
            array(),
            IBW_VERSION
        );
    } else {
        error_log( 'Interactive Bubbles Widget: CSS file not found at ' . $css_file );
    }

    // Enqueue JavaScript
    if ( file_exists( $js_file ) ) {
        wp_enqueue_script(
            'ibw-script',
            $js_url,
            array(),
            IBW_VERSION,
            true
        );
    } else {
        error_log( 'Interactive Bubbles Widget: JS file not found at ' . $js_file );
    }
}
add_action( 'wp_enqueue_scripts', 'ibw_enqueue_assets' );

/**
 * Plugin activation hook
 */
function ibw_activate_plugin() {
    // Log activation for debugging
    if ( defined( 'WP_DEBUG' ) && WP_DEBUG ) {
        error_log( 'Interactive Bubbles Widget plugin activated' );
        error_log( 'Plugin directory: ' . IBW_PLUGIN_PATH );
        error_log( 'Plugin URL: ' . IBW_PLUGIN_URL );
    }
}
register_activation_hook( __FILE__, 'ibw_activate_plugin' );

/**
 * Plugin deactivation hook
 */
function ibw_deactivate_plugin() {
    // Log deactivation for debugging
    if ( defined( 'WP_DEBUG' ) && WP_DEBUG ) {
        error_log( 'Interactive Bubbles Widget plugin deactivated' );
    }
}
register_deactivation_hook( __FILE__, 'ibw_deactivate_plugin' );

/**
 * Check if plugin files exist and show admin notice if not
 */
function ibw_check_plugin_files() {
    $css_file = IBW_PLUGIN_PATH . 'interactive-bubbles-widget.css';
    $js_file = IBW_PLUGIN_PATH . 'interactive-bubbles-widget.js';

    $missing_files = array();

    if ( ! file_exists( $css_file ) ) {
        $missing_files[] = 'interactive-bubbles-widget.css';
    }

    if ( ! file_exists( $js_file ) ) {
        $missing_files[] = 'interactive-bubbles-widget.js';
    }

    if ( ! empty( $missing_files ) ) {
        add_action( 'admin_notices', function() use ( $missing_files ) {
            echo '<div class="notice notice-error"><p>';
            echo '<strong>Interactive Bubbles Widget:</strong> Missing files: ' . implode( ', ', $missing_files );
            echo '<br>Plugin directory: ' . IBW_PLUGIN_PATH;
            echo '</p></div>';
        });
    }
}
add_action( 'admin_init', 'ibw_check_plugin_files' );

class Interactive_Bubbles_Widget extends WP_Widget {

    public function __construct() {
        parent::__construct(
            'interactive_bubbles_widget',
            __( 'Interactive Bubbles', 'interactive-bubbles-widget' ),
            array( 'description' => __( 'Goo bubbles + mouse effect with layout & typography controls.', 'interactive-bubbles-widget' ) )
        );
    }

    // Front‑end display
    public function widget( $args, $inst ) {
        // ---- Layout ----
        $layout = in_array( $inst['ibw_layout'] ?? 'column', ['column','row'], true )
                  ? $inst['ibw_layout'] : 'column';

        // ---- Text ----
        $title    = $inst['ibw_text']     ?? 'Bubbles';
        $subtitle = $inst['ibw_subtitle'] ?? '';
        $desc     = $inst['ibw_desc']     ?? '';

        // ---- Bubble Colors ----
        $bg1 = $inst['ibw_bg1'] ?? '#6c00a2';
        $bg2 = $inst['ibw_bg2'] ?? '#001152';
        $c2  = ibw_hex_to_rgb( $inst['ibw_c2']  ?? '#dd4aff' );
        $c3  = ibw_hex_to_rgb( $inst['ibw_c3']  ?? '#64dcff' );
        $c4  = ibw_hex_to_rgb( $inst['ibw_c4']  ?? '#c83232' );
        $c5  = ibw_hex_to_rgb( $inst['ibw_c5']  ?? '#b4b432' );
        $ci  = ibw_hex_to_rgb( $inst['ibw_ci']  ?? '#8c64ff' );

        // ---- Title Typography ----
        $t_color  = $inst['ibw_t_color']  ?? '#ffffff';

        // ---- Subtitle Typography ----
        $st_fam    = $inst['ibw_st_family'] ?? 'Dongle, sans-serif';
        $st_size   = intval( $inst['ibw_st_size'] ?? 48 ) . 'px';
        $st_wt     = intval( $inst['ibw_st_weight'] ?? 400 );
        $st_color  = $inst['ibw_st_color']  ?? '#ffffff';

        // ---- Description Typography ----
        $d_fam    = $inst['ibw_d_family'] ?? 'Dongle, sans-serif';
        $d_size   = intval( $inst['ibw_d_size'] ?? 24 ) . 'px';
        $d_wt     = intval( $inst['ibw_d_weight'] ?? 300 );
        $d_color  = $inst['ibw_d_color']  ?? '#ffffff';

        echo $args['before_widget'];

        // Generate unique ID for this widget instance
        $widget_id = 'ibw-' . uniqid();
        ?>
        <div class="interactive-bubbles-widget" id="<?php echo esc_attr($widget_id); ?>"
             style="
                --color-bg1: <?php echo esc_attr($bg1); ?>;
                --color-bg2: <?php echo esc_attr($bg2); ?>;
                --color1: <?php echo ibw_hex_to_rgb($bg1); ?>;
                --color2: <?php echo esc_attr($c2); ?>;
                --color3: <?php echo esc_attr($c3); ?>;
                --color4: <?php echo esc_attr($c4); ?>;
                --color5: <?php echo esc_attr($c5); ?>;
                --color-interactive: <?php echo esc_attr($ci); ?>;
                --circle-size: 500px;
                --blending: multiply;
             ">
          <div class="text-container"
               style="flex-direction: <?php echo esc_attr($layout); ?>;">
            <h1 class="ibw-title" style="color: <?php echo esc_attr($t_color); ?>;"><?php echo esc_html($title); ?></h1>
            <?php if ( $subtitle ) : ?>
              <h2 class="ibw-subtitle"
                  style="
                    font-family: <?php echo esc_attr($st_fam); ?>;
                    font-size:   <?php echo esc_attr($st_size); ?>;
                    font-weight: <?php echo esc_attr($st_wt); ?>;
                    color:       <?php echo esc_attr($st_color); ?>;
                  ">
                <?php echo esc_html($subtitle); ?>
              </h2>
            <?php endif; ?>
            <?php if ( $desc ) : ?>
              <p class="ibw-description"
                 style="
                   font-family: <?php echo esc_attr($d_fam); ?>;
                   font-size:   <?php echo esc_attr($d_size); ?>;
                   font-weight: <?php echo esc_attr($d_wt); ?>;
                   color:       <?php echo esc_attr($d_color); ?>;
                 ">
                <?php echo esc_html($desc); ?>
              </p>
            <?php endif; ?>
          </div>

          <div class="gradient-bg">
            <svg xmlns="http://www.w3.org/2000/svg"><defs>
              <filter id="goo-<?php echo esc_attr($widget_id); ?>">
                <feGaussianBlur in="SourceGraphic" stdDeviation="10" result="blur"/>
                <feColorMatrix in="blur" mode="matrix"
                  values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 18 -8"
                  result="goo"/>
                <feBlend in="SourceGraphic" in2="goo"/>
              </filter>
            </defs></svg>
            <div class="gradients-container" style="filter: url(#goo-<?php echo esc_attr($widget_id); ?>) blur(5px);">
              <div class="g1"></div>
              <div class="g2"></div>
              <div class="g3"></div>
              <div class="g4"></div>
              <div class="g5"></div>
              <div class="interactive"></div>
            </div>
          </div>
        </div>
        <?php
        echo $args['after_widget'];
    }

    // Backend form
    public function form( $inst ) {
        // Load or default
        $title    = esc_attr( $inst['ibw_text']     ?? 'Bubbles' );
        $subtitle = esc_attr( $inst['ibw_subtitle'] ?? '' );
        $desc     = esc_textarea( $inst['ibw_desc'] ?? '' );
        $layout   = $inst['ibw_layout'] ?? 'column';

        // Colors
        $bg1 = esc_attr( $inst['ibw_bg1'] ?? '#6c00a2' );
        $bg2 = esc_attr( $inst['ibw_bg2'] ?? '#001152' );
        $c2  = esc_attr( $inst['ibw_c2']  ?? '#dd4aff' );
        $c3  = esc_attr( $inst['ibw_c3']  ?? '#64dcff' );
        $c4  = esc_attr( $inst['ibw_c4']  ?? '#c83232' );
        $c5  = esc_attr( $inst['ibw_c5']  ?? '#b4b432' );
        $ci  = esc_attr( $inst['ibw_ci']  ?? '#8c64ff' );

        // Title typography
        $t_color  = esc_attr( $inst['ibw_t_color'] ?? '#ffffff' );

        // Subtitle typography values
        $st_fam    = esc_attr( $inst['ibw_st_family'] ?? 'Dongle, sans-serif' );
        $st_size_v = intval( $inst['ibw_st_size'] ?? 48 );
        $st_wt_v   = intval( $inst['ibw_st_weight'] ?? 400 );
        $st_color  = esc_attr( $inst['ibw_st_color'] ?? '#ffffff' );

        // Description typography
        $d_fam    = esc_attr( $inst['ibw_d_family'] ?? 'Dongle, sans-serif' );
        $d_size_v = intval( $inst['ibw_d_size'] ?? 24 );
        $d_wt_v   = intval( $inst['ibw_d_weight'] ?? 300 );
        $d_color  = esc_attr( $inst['ibw_d_color'] ?? '#ffffff' );
        ?>
        <!-- Title Text -->
        <p>
          <label><?php _e('Title Text:', 'interactive-bubbles-widget'); ?></label>
          <input class="widefat"
                 name="<?php echo $this->get_field_name('ibw_text'); ?>"
                 value="<?php echo $title; ?>">
        </p>
        <p>
          <label><?php _e('Title Color:', 'interactive-bubbles-widget'); ?></label><br>
          <input type="color"
                 name="<?php echo $this->get_field_name('ibw_t_color'); ?>"
                 value="<?php echo $t_color; ?>">
        </p>

        <!-- Subtitle & Description -->
        <p>
          <label><?php _e('Subtitle:', 'interactive-bubbles-widget'); ?></label>
          <input class="widefat"
                 name="<?php echo $this->get_field_name('ibw_subtitle'); ?>"
                 value="<?php echo $subtitle; ?>">
        </p>
        <p>
          <label><?php _e('Description:', 'interactive-bubbles-widget'); ?></label>
          <textarea class="widefat" rows="3"
                    name="<?php echo $this->get_field_name('ibw_desc'); ?>"><?php echo $desc; ?></textarea>
        </p>

        <!-- Layout Selector -->
        <p>
          <label><?php _e('Layout:', 'interactive-bubbles-widget'); ?></label><br>
          <select name="<?php echo $this->get_field_name('ibw_layout'); ?>">
            <option value="column" <?php selected($layout,'column'); ?>>
              <?php _e('Vertical (stacked)', 'interactive-bubbles-widget'); ?>
            </option>
            <option value="row" <?php selected($layout,'row'); ?>>
              <?php _e('Horizontal (side-by-side)', 'interactive-bubbles-widget'); ?>
            </option>
          </select>
        </p>

        <!-- Subtitle Typography -->
        <hr>
        <p><strong><?php _e('Subtitle Typography', 'interactive-bubbles-widget'); ?></strong></p>
        <p>
          <label><?php _e('Font Family:', 'interactive-bubbles-widget'); ?></label>
          <select class="widefat"
                  name="<?php echo $this->get_field_name('ibw_st_family'); ?>">
            <?php
            $fonts = [
              'Dongle, sans-serif'        => 'Dongle',
              'Arial, sans-serif'         => 'Arial',
              'Georgia, serif'            => 'Georgia',
              "'Playfair Display', serif" => 'Playfair Display',
              "'Roboto', sans-serif"      => 'Roboto',
            ];
            foreach ( $fonts as $val => $label ) {
                printf(
                  '<option value="%s"%s>%s</option>',
                  esc_attr($val),
                  selected($st_fam,$val,false),
                  esc_html($label)
                );
            }
            ?>
          </select>
        </p>
        <p>
          <label><?php _e('Font Size:', 'interactive-bubbles-widget'); ?> <span id="st-size-val"><?php echo $st_size_v; ?>px</span></label><br>
          <input type="range" min="10" max="100" step="1"
                 id="st-size-slider"
                 name="<?php echo $this->get_field_name('ibw_st_size'); ?>"
                 value="<?php echo $st_size_v; ?>"
                 oninput="this.nextElementSibling && (document.getElementById('st-size-val').innerText = this.value+'px')">
        </p>
        <p>
          <label><?php _e('Font Weight:', 'interactive-bubbles-widget'); ?> <span id="st-wt-val"><?php echo $st_wt_v; ?></span></label><br>
          <input type="range" min="100" max="900" step="100"
                 id="st-wt-slider"
                 name="<?php echo $this->get_field_name('ibw_st_weight'); ?>"
                 value="<?php echo $st_wt_v; ?>"
                 oninput="this.nextElementSibling && (document.getElementById('st-wt-val').innerText = this.value)">
        </p>
        <p>
          <label><?php _e('Color:', 'interactive-bubbles-widget'); ?></label><br>
          <input type="color"
                 name="<?php echo $this->get_field_name('ibw_st_color'); ?>"
                 value="<?php echo $st_color; ?>">
        </p>

        <!-- Description Typography -->
        <hr>
        <p><strong><?php _e('Description Typography', 'interactive-bubbles-widget'); ?></strong></p>
        <p>
          <label><?php _e('Font Family:', 'interactive-bubbles-widget'); ?></label>
          <select class="widefat"
                  name="<?php echo $this->get_field_name('ibw_d_family'); ?>">
            <?php foreach ( $fonts as $val => $label ) {
                printf(
                  '<option value="%s"%s>%s</option>',
                  esc_attr($val),
                  selected($d_fam,$val,false),
                  esc_html($label)
                );
            } ?>
          </select>
        </p>
        <p>
          <label><?php _e('Font Size:', 'interactive-bubbles-widget'); ?> <span id="d-size-val"><?php echo $d_size_v; ?>px</span></label><br>
          <input type="range" min="10" max="80" step="1"
                 id="d-size-slider"
                 name="<?php echo $this->get_field_name('ibw_d_size'); ?>"
                 value="<?php echo $d_size_v; ?>"
                 oninput="document.getElementById('d-size-val').innerText = this.value+'px'">
        </p>
        <p>
          <label><?php _e('Font Weight:', 'interactive-bubbles-widget'); ?> <span id="d-wt-val"><?php echo $d_wt_v; ?></span></label><br>
          <input type="range" min="100" max="900" step="100"
                 id="d-wt-slider"
                 name="<?php echo $this->get_field_name('ibw_d_weight'); ?>"
                 value="<?php echo $d_wt_v; ?>"
                 oninput="document.getElementById('d-wt-val').innerText = this.value">
        </p>
        <p>
          <label><?php _e('Color:', 'interactive-bubbles-widget'); ?></label><br>
          <input type="color"
                 name="<?php echo $this->get_field_name('ibw_d_color'); ?>"
                 value="<?php echo $d_color; ?>">
        </p>

        <!-- Bubble Colors (unchanged) -->
        <hr>
        <p><strong><?php _e('Bubble Colors', 'interactive-bubbles-widget'); ?></strong></p>
        <p>
          <label><?php _e('BG Color 1:', 'interactive-bubbles-widget'); ?></label><br>
          <input type="color"
                 name="<?php echo $this->get_field_name('ibw_bg1'); ?>"
                 value="<?php echo $bg1; ?>">
        </p>
        <p>
          <label><?php _e('BG Color 2:', 'interactive-bubbles-widget'); ?></label><br>
          <input type="color"
                 name="<?php echo $this->get_field_name('ibw_bg2'); ?>"
                 value="<?php echo $bg2; ?>">
        </p>
        <?php foreach ( [2,3,4,5] as $i ) :
            $field = "ibw_c{$i}";
            $val   = ${"c{$i}"};
        ?>
          <p>
            <label><?php printf( __( 'Color %d:', 'interactive-bubbles-widget' ), $i ); ?></label><br>
            <input type="color"
                   name="<?php echo $this->get_field_name($field); ?>"
                   value="<?php echo esc_attr($inst[$field] ?? "#".dechex(rand(0,0xffffff))); ?>">
          </p>
        <?php endforeach; ?>
        <p>
          <label><?php _e('Interactive Bubble:', 'interactive-bubbles-widget'); ?></label><br>
          <input type="color"
                 name="<?php echo $this->get_field_name('ibw_ci'); ?>"
                 value="<?php echo $ci; ?>">
        </p>
        <?php
    }

    // Sanitize & save
    public function update( $new, $old ) {
        $out = [];
        $out['ibw_text']       = sanitize_text_field( $new['ibw_text'] );
        $out['ibw_subtitle']   = sanitize_text_field( $new['ibw_subtitle'] );
        $out['ibw_desc']       = sanitize_textarea_field( $new['ibw_desc'] );
        $out['ibw_layout']     = in_array($new['ibw_layout'], ['column','row'], true)
                                 ? $new['ibw_layout'] : 'column';
        $out['ibw_bg1']        = sanitize_hex_color( $new['ibw_bg1'] );
        $out['ibw_bg2']        = sanitize_hex_color( $new['ibw_bg2'] );
        foreach ( [2,3,4,5] as $i ) {
            $out["ibw_c{$i}"] = sanitize_hex_color( $new["ibw_c{$i}"] );
        }
        $out['ibw_ci']         = sanitize_hex_color( $new['ibw_ci'] );
        // title typography
        $out['ibw_t_color']    = sanitize_hex_color( $new['ibw_t_color'] );
        // subtitle typography
        $out['ibw_st_family']  = sanitize_text_field( $new['ibw_st_family'] );
        $out['ibw_st_size']    = intval( $new['ibw_st_size'] );
        $out['ibw_st_weight']  = intval( $new['ibw_st_weight'] );
        $out['ibw_st_color']   = sanitize_hex_color( $new['ibw_st_color'] );
        // desc typography
        $out['ibw_d_family']   = sanitize_text_field( $new['ibw_d_family'] );
        $out['ibw_d_size']     = intval( $new['ibw_d_size'] );
        $out['ibw_d_weight']   = intval( $new['ibw_d_weight'] );
        $out['ibw_d_color']    = sanitize_hex_color( $new['ibw_d_color'] );
        return $out;
    }
}

// Register widget
add_action( 'widgets_init', function(){
    register_widget( 'Interactive_Bubbles_Widget' );
} );
