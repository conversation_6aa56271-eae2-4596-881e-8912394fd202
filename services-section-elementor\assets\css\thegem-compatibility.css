/**
 * TheGem Theme Compatibility Styles
 * Services Section for Elementor
 * Version: 1.0.0
 */

/* Reset TheGem's default styles that might conflict */
.elementor-widget-services-section-commandnet * {
    box-sizing: border-box !important;
}

/* Ensure TheGem's container styles don't interfere */
.elementor-widget-services-section-commandnet .services-section-commandnet {
    width: 100% !important;
    max-width: none !important;
    margin: 0 !important;
    padding: 5rem 2rem !important;
}

/* Override TheGem's typography settings */
.elementor-widget-services-section-commandnet .services-title-commandnet {
    font-family: inherit !important;
    line-height: 1.375 !important;
    letter-spacing: normal !important;
    text-transform: none !important;
}

.elementor-widget-services-section-commandnet .card-title-commandnet {
    font-family: "Playfair Display", serif !important;
    font-weight: 400 !important;
    line-height: 1.2 !important;
    letter-spacing: normal !important;
}

/* Ensure TheGem's grid system doesn't interfere */
.elementor-widget-services-section-commandnet .grid-offer-commandnet {
    display: grid !important;
    grid-template-columns: 1fr !important;
    gap: 1.25rem !important;
    width: 100% !important;
}

/* Override TheGem's button and link styles */
.elementor-widget-services-section-commandnet a,
.elementor-widget-services-section-commandnet button {
    text-decoration: none !important;
    border: none !important;
    background: transparent !important;
    box-shadow: none !important;
}

/* Ensure hover effects work with TheGem */
.elementor-widget-services-section-commandnet .card-commandnet {
    position: relative !important;
    overflow: hidden !important;
    transform: translateZ(0) !important; /* Force hardware acceleration */
}

.elementor-widget-services-section-commandnet .card-commandnet::before {
    position: absolute !important;
    content: "" !important;
    width: 100% !important;
    height: 100% !important;
    transition: all 0.6s ease !important;
    z-index: 0 !important;
    background-color: #4f46e5 !important;
    will-change: clip-path !important;
}

/* Responsive compatibility with TheGem's breakpoints */
@media (min-width: 640px) {
    .elementor-widget-services-section-commandnet .grid-offer-commandnet {
        grid-template-columns: repeat(2, 1fr) !important;
    }
}

@media (min-width: 1024px) {
    .elementor-widget-services-section-commandnet .services-section-commandnet {
        padding: 5rem 0 !important;
    }
}

/* Ensure TheGem's color scheme doesn't override our colors */
.elementor-widget-services-section-commandnet .services-section-commandnet {
    background-color: #111827 !important;
    color: inherit !important;
}

.elementor-widget-services-section-commandnet .services-subtitle-commandnet {
    color: #9ca3af !important;
}

.elementor-widget-services-section-commandnet .services-title-commandnet {
    color: white !important;
}

.elementor-widget-services-section-commandnet .card-commandnet {
    background-color: #1f2937 !important;
}

.elementor-widget-services-section-commandnet .card-title-commandnet {
    color: white !important;
}

.elementor-widget-services-section-commandnet .card-description-commandnet {
    color: #9ca3af !important;
}

/* Fix potential z-index conflicts with TheGem */
.elementor-widget-services-section-commandnet {
    position: relative !important;
    z-index: 1 !important;
}

.elementor-widget-services-section-commandnet .card-content-commandnet {
    position: relative !important;
    z-index: 2 !important;
}

/* Ensure smooth animations work with TheGem */
.elementor-widget-services-section-commandnet .card-commandnet:hover::before {
    clip-path: circle(110vw at 100% 100%) !important;
}

.elementor-widget-services-section-commandnet .card-commandnet:hover .card-description-commandnet {
    color: #fff !important;
}

/* TheGem specific fixes for Elementor editor */
.elementor-editor-active .elementor-widget-services-section-commandnet .card-commandnet::before {
    pointer-events: none !important;
}

/* Ensure proper spacing in TheGem containers */
.thegem-container .elementor-widget-services-section-commandnet,
.gem-container .elementor-widget-services-section-commandnet {
    width: 100% !important;
    max-width: none !important;
}
