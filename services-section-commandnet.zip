services-section-commandnet/                                                                        0000755 0601751 0601001 00000000000 15042751652 014225  5                                                                                                    ustar   <PERSON>hmd                                                                                                                                                                                                                                                   services-section-commandnet/assets/                                                                 0000755 0601751 0601001 00000000000 15042751657 015534  5                                                                                                    ustar   Mhmd                                                                                                                                                                                                                                                   services-section-commandnet/assets/css/                                                             0000755 0601751 0601001 00000000000 15042751664 016322  5                                                                                                    ustar   Mhmd                                                                                                                                                                                                                                                   services-section-commandnet/assets/css/index.php                                                    0000644 0601751 0601001 00000000034 15042751664 020137  0                                                                                                    ustar   Mhmd                                                                                                                                                                                                                                                   <?php
// Silence is golden.
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    services-section-commandnet/assets/css/services-section-commandnet.css                              0000644 0601751 0601001 00000013164 15042751367 024451  0                                                                                                    ustar   Mhmd                                                                                                                                                                                                                                                   /**
 * Services Section CommandNet Styles
 * Version: 1.0.0
 */

@import url("https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400;1,400&display=swap");

/* Reset and base styles */
* {
  box-sizing: border-box;
}

/* Services section - replaces Tailwind classes */
.services-section-commandnet {
  min-height: 100vh;
  background-color: #111827; /* bg-gray-900 */
  text-align: center;
  padding: 5rem 2rem; /* py-20 px-8 */
  display: flex;
  flex-direction: column;
  justify-content: center;
}

/* Services subtitle - replaces Tailwind classes */
.services-subtitle-commandnet {
  color: #9ca3af; /* text-gray-400 */
  font-size: 1.125rem; /* text-lg */
  max-width: 32rem; /* max-w-lg */
  margin: 0 auto 0.5rem auto; /* mx-auto mb-2 */
  text-transform: capitalize;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Arrow icon - replaces Tailwind classes */
.arrow-icon-commandnet {
  color: #4f46e5; /* text-indigo-600 */
  margin-left: 0.75rem; /* ml-3 */
  width: 1.5rem; /* w-6 */
  height: 1.5rem; /* h-6 */
}

/* Services title - replaces Tailwind classes */
.services-title-commandnet {
  color: white; /* text-white */
  font-size: 2.25rem; /* text-4xl */
  font-weight: 600; /* font-semibold */
  max-width: 48rem; /* max-w-3xl */
  margin: 0 auto 4rem auto; /* mx-auto mb-16 */
  line-height: 1.375; /* leading-snug */
}

/* Grid container - replaces Tailwind classes */
.grid-offer-commandnet {
  text-align: left;
  display: grid;
  gap: 1.25rem; /* gap-5 */
  max-width: 80rem; /* max-w-5xl */
  margin: 0 auto;
  grid-template-columns: 1fr;
}

/* Card base styles - replaces Tailwind classes */
.card-commandnet {
  background-color: #1f2937; /* bg-gray-800 */
  padding: 2.5rem; /* p-10 */
  position: relative;
}

/* Card content positioning */
.card-content-commandnet {
  position: relative;
  z-index: 1;
}

.card-content-right-commandnet {
  padding-right: 0;
}

.card-content-left-commandnet {
  padding-left: 0;
}

/* Card title - replaces Tailwind classes */
.card-title-commandnet {
  text-transform: capitalize;
  color: white; /* text-white */
  margin-bottom: 1rem; /* mb-4 */
  font-size: 1.5rem; /* text-2xl */
  font-family: "Playfair Display", serif;
  font-optical-sizing: auto;
  font-weight: 400;
  font-style: normal;
}

/* Card description - replaces Tailwind classes */
.card-description-commandnet {
  color: #9ca3af; /* text-gray-400 */
  transition: 0.8s;
}

/* Responsive design */
@media (min-width: 640px) {
  .grid-offer-commandnet {
    grid-template-columns: repeat(2, 1fr); /* sm:grid-cols-2 */
  }
}

@media (min-width: 768px) {
  .services-title-commandnet {
    font-size: 3rem; /* md:text-5xl */
  }
}

@media (min-width: 1024px) {
  .card-content-right-commandnet {
    padding-right: 13rem; /* lg:pr-52 */
  }

  .card-content-left-commandnet {
    padding-left: 12rem; /* lg:pl-48 */
  }

  .card-commandnet:nth-child(3) .card-content-right-commandnet {
    padding-right: 11rem; /* lg:pr-44 */
  }
}

@media (min-width: 1280px) {
  .services-section-commandnet {
    padding-left: 0; /* xl:px-0 */
    padding-right: 0;
  }

  .services-title-commandnet {
    font-size: 3.75rem; /* xl:text-6xl */
  }

  .card-title-commandnet {
    font-size: 1.875rem; /* xl:text-3xl */
  }
}

/* Existing card hover effects and animations */
.card-commandnet::before {
  position: absolute;
  content: "";
  width: 100%;
  height: 100%;
  transition: 0.6s;
  z-index: 0;
  background-color: #4f46e5;
}

.card-commandnet:hover {
  box-shadow: 0.063rem 0.063rem 1.25rem 0.375rem rgba(0, 0, 0, 0.53);
}

.card-commandnet:nth-child(1)::before {
  bottom: 0;
  right: 0;
  clip-path: circle(calc(6.25rem + 7.5vw) at 100% 100%);
}

.card-commandnet:nth-child(2)::before {
  bottom: 0;
  left: 0;
  clip-path: circle(calc(6.25rem + 7.5vw) at 0% 100%);
}

.card-commandnet:nth-child(3)::before {
  top: 0;
  right: 0;
  clip-path: circle(calc(6.25rem + 7.5vw) at 100% 0%);
}

.card-commandnet:nth-child(4)::before {
  top: 0;
  left: 0;
  clip-path: circle(calc(6.25rem + 7.5vw) at 0% 0%);
}

.card-commandnet:hover::before {
  clip-path: circle(110vw at 100% 100%);
}

.card-commandnet:hover .card-description-commandnet {
  color: #fff;
}

@media screen and (min-width: 62.5rem) {
  .circle-commandnet {
    position: absolute;
    width: 100%;
    height: 100%;
    z-index: 0;
  }
}

.card-commandnet:nth-child(1) .circle-commandnet {
  background: url("https://images.unsplash.com/photo-1587440871875-191322ee64b0?q=80&w=2071&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D") no-repeat 50% 50%/cover;
  bottom: 0;
  right: 0;
  clip-path: circle(calc(6.25rem + 7.5vw) at 100% 100%);
}

.card-commandnet:nth-child(2) .circle-commandnet {
  background: url("https://images.unsplash.com/photo-1499951360447-b19be8fe80f5?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D") no-repeat 50% 50%/cover;
  bottom: 0;
  left: 0;
  clip-path: circle(calc(6.25rem + 7.5vw) at 0% 100%);
}

.card-commandnet:nth-child(3) .circle-commandnet {
  background: url("https://images.unsplash.com/photo-1557804506-669a67965ba0?q=80&w=1974&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D") no-repeat 50% 50%/cover;
  top: 0;
  right: 0;
  clip-path: circle(calc(6.25rem + 7.5vw) at 100% 0%);
}

.card-commandnet:nth-child(4) .circle-commandnet {
  background: url("https://images.unsplash.com/photo-1600880292203-757bb62b4baf?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D") no-repeat 50% 50%/cover;
  top: 0;
  left: 0;
  clip-path: circle(calc(6.25rem + 7.5vw) at 0% 0%);
}
                                                                                                                                                                                                                                                                                                                                                                                                            services-section-commandnet/assets/index.php                                                        0000644 0601751 0601001 00000000034 15042751657 017351  0                                                                                                    ustar   Mhmd                                                                                                                                                                                                                                                   <?php
// Silence is golden.
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    services-section-commandnet/index.php                                                               0000644 0601751 0601001 00000000034 15042751652 016042  0                                                                                                    ustar   Mhmd                                                                                                                                                                                                                                                   <?php
// Silence is golden.
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    services-section-commandnet/readme.txt                                                              0000644 0601751 0601001 *********** *********** 016232  0                                                                                                    ustar   Mhmd                                                                                                                                                                                                                                                   === Services Section CommandNet ===
Contributors: yourname
Tags: services, section, responsive, shortcode, business
Requires at least: 5.0
Tested up to: 6.4
Requires PHP: 7.4
Stable tag: 1.0.0
License: GPLv2 or later
License URI: https://www.gnu.org/licenses/gpl-2.0.html

A responsive services section with hover effects and animations for showcasing business services.

== Description ==

Services Section CommandNet is a WordPress plugin that provides a beautiful, responsive services section with hover effects and animations. Perfect for showcasing your business services in an engaging way.

**Features:**

* Responsive design that works on all devices
* Beautiful hover effects with clip-path animations
* Background images for each service card
* Customizable title and subtitle
* Easy to use shortcode
* Translation ready
* No conflicts with other plugins (uses -commandnet namespace)

**Usage:**

Simply use the shortcode `[services_section_commandnet]` in any post, page, or widget to display the services section.

**Shortcode Attributes:**

* `class` - Add custom CSS classes
* `title` - Customize the main title (default: "Services Built Specifically for your Business")
* `subtitle` - Customize the subtitle (default: "what we're offering")

**Example:**
`[services_section_commandnet title="Our Amazing Services" subtitle="what we offer" class="my-custom-class"]`

== Installation ==

1. Upload the plugin files to the `/wp-content/plugins/services-section-commandnet` directory, or install the plugin through the WordPress plugins screen directly.
2. Activate the plugin through the 'Plugins' screen in WordPress
3. Use the shortcode `[services_section_commandnet]` in your posts, pages, or widgets

== Frequently Asked Questions ==

= How do I customize the content of the service cards? =

Currently, the service cards content is fixed. Future versions will include customization options for the service cards content.

= Can I change the background images? =

The background images are currently set to specific Unsplash images. Future versions will include options to customize these images.

= Does this plugin conflict with other CSS frameworks? =

No, all CSS classes use the `-commandnet` suffix to prevent conflicts with other plugins and themes.

== Screenshots ==

1. Services section display on frontend
2. Hover effects demonstration
3. Responsive design on mobile devices

== Changelog ==

= 1.0.0 =
* Initial release
* Responsive services section with hover effects
* Shortcode implementation
* Translation ready
* WordPress coding standards compliance

== Upgrade Notice ==

= 1.0.0 =
Initial release of Services Section CommandNet plugin.
                                                                                                                                                                                                                                                                                                                                                                                                               services-section-commandnet/services-section-commandnet.php                                         0000644 0601751 0601001 00000011726 15042751530 022350  0                                                                                                    ustar   Mhmd                                                                                                                                                                                                                                                   <?php
/**
 * Plugin Name: Services Section CommandNet
 * Plugin URI: https://example.com/services-section-commandnet
 * Description: A responsive services section with hover effects and animations. Use shortcode [services_section_commandnet] to display.
 * Version: 1.0.0
 * Author: Your Name
 * Author URI: https://example.com
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: services-section-commandnet
 * Domain Path: /languages
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('SERVICES_SECTION_COMMANDNET_VERSION', '1.0.0');
define('SERVICES_SECTION_COMMANDNET_PLUGIN_URL', plugin_dir_url(__FILE__));
define('SERVICES_SECTION_COMMANDNET_PLUGIN_PATH', plugin_dir_path(__FILE__));

/**
 * Main plugin class
 */
class ServicesSection_CommandNet {
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('init', array($this, 'init'));
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
    }
    
    /**
     * Initialize the plugin
     */
    public function init() {
        // Add shortcode
        add_shortcode('services_section_commandnet', array($this, 'render_services_section'));

        // Enqueue styles and scripts
        add_action('wp_enqueue_scripts', array($this, 'enqueue_assets'));

        // Add admin notices
        add_action('admin_notices', array($this, 'admin_notices'));

        // Load text domain for translations
        load_plugin_textdomain('services-section-commandnet', false, dirname(plugin_basename(__FILE__)) . '/languages');
    }
    
    /**
     * Plugin activation
     */
    public function activate() {
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    /**
     * Enqueue CSS and JS files
     */
    public function enqueue_assets() {
        // Only enqueue if shortcode is being used on the current page
        global $post;

        // Check if we're on a page/post and the shortcode exists
        if (is_a($post, 'WP_Post') && has_shortcode($post->post_content, 'services_section_commandnet')) {
            $this->enqueue_styles();
        }

        // Also check for widgets and other content areas
        if (is_active_widget(false, false, 'text') || is_customize_preview()) {
            $this->enqueue_styles();
        }
    }

    /**
     * Enqueue the plugin styles
     */
    private function enqueue_styles() {
        wp_enqueue_style(
            'services-section-commandnet-style',
            SERVICES_SECTION_COMMANDNET_PLUGIN_URL . 'assets/css/services-section-commandnet.css',
            array(),
            SERVICES_SECTION_COMMANDNET_VERSION,
            'all'
        );
    }
    
    /**
     * Render the services section shortcode
     */
    public function render_services_section($atts) {
        // Parse shortcode attributes with defaults
        $atts = shortcode_atts(array(
            'class' => '',
            'title' => 'Services Built Specifically for your Business',
            'subtitle' => "what we're offering"
        ), $atts, 'services_section_commandnet');

        // Sanitize attributes
        $extra_class = sanitize_html_class($atts['class']);
        $title = sanitize_text_field($atts['title']);
        $subtitle = sanitize_text_field($atts['subtitle']);

        // Ensure styles are enqueued
        $this->enqueue_styles();

        // Start output buffering
        ob_start();

        // Check if template file exists
        $template_path = SERVICES_SECTION_COMMANDNET_PLUGIN_PATH . 'templates/services-section.php';
        if (file_exists($template_path)) {
            include $template_path;
        } else {
            // Fallback error message for administrators
            if (current_user_can('manage_options')) {
                echo '<div class="error"><p>' . esc_html__('Services Section CommandNet: Template file not found.', 'services-section-commandnet') . '</p></div>';
            }
        }

        // Return the buffered content
        return ob_get_clean();
    }

    /**
     * Add admin notice for missing dependencies
     */
    public function admin_notices() {
        // Check if required files exist
        $css_file = SERVICES_SECTION_COMMANDNET_PLUGIN_PATH . 'assets/css/services-section-commandnet.css';
        $template_file = SERVICES_SECTION_COMMANDNET_PLUGIN_PATH . 'templates/services-section.php';

        if (!file_exists($css_file) || !file_exists($template_file)) {
            echo '<div class="notice notice-error"><p>';
            echo esc_html__('Services Section CommandNet: Some plugin files are missing. Please reinstall the plugin.', 'services-section-commandnet');
            echo '</p></div>';
        }
    }
}

// Initialize the plugin
new ServicesSection_CommandNet();
                                          services-section-commandnet/templates/                                                              0000755 0601751 0601001 00000000000 15042751672 016225  5                                                                                                    ustar   Mhmd                                                                                                                                                                                                                                                   services-section-commandnet/templates/index.php                                                     0000644 0601751 0601001 00000000034 15042751672 020042  0                                                                                                    ustar   Mhmd                                                                                                                                                                                                                                                   <?php
// Silence is golden.
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    services-section-commandnet/templates/services-section.php                                          0000644 0601751 0601001 00000006666 15042751546 022241  0                                                                                                    ustar   Mhmd                                                                                                                                                                                                                                                   <?php
/**
 * Services Section Template
 * 
 * This template is used by the [services_section_commandnet] shortcode
 * 
 * @package ServicesSection_CommandNet
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}
?>

<section class="services-section-commandnet <?php echo esc_attr($extra_class); ?>">
  <span class="services-subtitle-commandnet">
    <?php echo esc_html($subtitle); ?>
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="arrow-icon-commandnet">
      <path stroke-linecap="round" stroke-linejoin="round" d="M17.25 8.25 21 12m0 0-3.75 3.75M21 12H3" />
    </svg>
  </span>

  <h1 class="services-title-commandnet">
    <?php echo esc_html($title); ?>
  </h1>
  
  <div class="grid-offer-commandnet">
    <div class="card-commandnet">
      <div class="circle-commandnet"></div>
      <div class="card-content-commandnet card-content-right-commandnet">
        <h2 class="card-title-commandnet">
          <?php esc_html_e('UI/UX', 'services-section-commandnet'); ?><br />
          <?php esc_html_e('creative design', 'services-section-commandnet'); ?>
        </h2>
        <p class="card-description-commandnet">
          <?php esc_html_e('Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Pellentesque habitant morbi tristique senectus et netus et malesuada fames.', 'services-section-commandnet'); ?>
        </p>
      </div>
      <div class="icon-commandnet"></div>
    </div>
    
    <div class="card-commandnet">
      <div class="circle-commandnet"></div>
      <div class="card-content-commandnet card-content-left-commandnet">
        <h2 class="card-title-commandnet">
          <?php esc_html_e('visual', 'services-section-commandnet'); ?><br />
          <?php esc_html_e('graphic design', 'services-section-commandnet'); ?>
        </h2>
        <p class="card-description-commandnet">
          <?php esc_html_e('Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Pellentesque habitant morbi tristique senectus et netus et malesuada fames.', 'services-section-commandnet'); ?>
        </p>
      </div>
    </div>
    
    <div class="card-commandnet">
      <div class="circle-commandnet"></div>
      <div class="card-content-commandnet card-content-right-commandnet">
        <h2 class="card-title-commandnet">
          <?php esc_html_e('strategy &', 'services-section-commandnet'); ?><br />
          <?php esc_html_e('digital marketing', 'services-section-commandnet'); ?>
        </h2>
        <p class="card-description-commandnet">
          <?php esc_html_e('Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Pellentesque habitant morbi tristique senectus et netus et malesuada fames.', 'services-section-commandnet'); ?>
        </p>
      </div>
    </div>
    
    <div class="card-commandnet">
      <div class="circle-commandnet"></div>
      <div class="card-content-commandnet card-content-left-commandnet">
        <h2 class="card-title-commandnet">
          <?php esc_html_e('effective', 'services-section-commandnet'); ?><br />
          <?php esc_html_e('business growth', 'services-section-commandnet'); ?>
        </h2>
        <p class="card-description-commandnet">
          <?php esc_html_e('Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Pellentesque habitant morbi tristique senectus et netus et malesuada fames.', 'services-section-commandnet'); ?>
        </p>
      </div>
    </div>
  </div>
</section>
                                                                          services-section-commandnet/uninstall.php                                                           0000644 0601751 0601001 *********** *********** 016744  0                                                                                                    ustar   Mhmd                                                                                                                                                                                                                                                   <?php
/**
 * Uninstall Services Section CommandNet
 *
 * @package ServicesSection_CommandNet
 * @version 1.0.0
 */

// If uninstall not called from WordPress, then exit.
if (!defined('WP_UNINSTALL_PLUGIN')) {
    exit;
}

// Clean up any options or data if needed in future versions
// Currently, this plugin doesn't store any data in the database

// Clear any cached data
wp_cache_flush();
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        