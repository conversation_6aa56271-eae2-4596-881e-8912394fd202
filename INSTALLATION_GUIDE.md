# Services Section CommandNet - Installation Guide

## Overview
Services Section CommandNet is a WordPress plugin that provides a beautiful, responsive services section with hover effects and animations.

## Installation Instructions

### Method 1: WordPress Admin Dashboard (Recommended)
1. Download the `services-section-commandnet.zip` file
2. Log in to your WordPress admin dashboard
3. Navigate to **Plugins > Add New**
4. Click **Upload Plugin**
5. Choose the `services-section-commandnet.zip` file
6. Click **Install Now**
7. Click **Activate Plugin**

### Method 2: FTP Upload
1. Extract the `services-section-commandnet.zip` file
2. Upload the `services-section-commandnet` folder to `/wp-content/plugins/`
3. Log in to your WordPress admin dashboard
4. Navigate to **Plugins > Installed Plugins**
5. Find "Services Section CommandNet" and click **Activate**

## Usage

### Basic Usage
Add the following shortcode to any post, page, or widget:
```
[services_section_commandnet]
```

### Advanced Usage with Custom Attributes
```
[services_section_commandnet title="Our Amazing Services" subtitle="what we offer" class="my-custom-class"]
```

### Available Shortcode Attributes
- `title` - Customize the main title (default: "Services Built Specifically for your Business")
- `subtitle` - Customize the subtitle (default: "what we're offering")
- `class` - Add custom CSS classes for additional styling

## Features
- ✅ Responsive design that works on all devices
- ✅ Beautiful hover effects with clip-path animations
- ✅ Background images for each service card
- ✅ Customizable title and subtitle
- ✅ Easy to use shortcode
- ✅ Translation ready
- ✅ No conflicts with other plugins (uses -commandnet namespace)
- ✅ WordPress coding standards compliant
- ✅ Secure and optimized

## File Structure
```
services-section-commandnet/
├── assets/
│   ├── css/
│   │   ├── services-section-commandnet.css
│   │   └── index.php
│   └── index.php
├── templates/
│   ├── services-section.php
│   └── index.php
├── index.php
├── readme.txt
├── services-section-commandnet.php (main plugin file)
└── uninstall.php
```

## Requirements
- WordPress 5.0 or higher
- PHP 7.4 or higher

## Support
For support and customization requests, please contact the plugin author.

## License
This plugin is licensed under GPL v2 or later.
