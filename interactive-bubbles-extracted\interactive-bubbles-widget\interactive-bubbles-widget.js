

"use strict";

// Debug logging
console.log('Interactive Bubbles Widget JS loaded');

document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded, initializing Interactive Bubbles Widget');
    const widgets = document.querySelectorAll('.interactive-bubbles-widget');
    console.log('Found', widgets.length, 'Interactive Bubbles Widget instances');

    widgets.forEach((widget, index) => {
        console.log('Initializing widget', index + 1);
        const interBubble = widget.querySelector('.interactive');
        if (!interBubble) {
            console.warn('Interactive bubble element not found in widget', index + 1);
            return;
        }
        console.log('Interactive bubble found for widget', index + 1);

        let curX = 0, curY = 0, tgX = 0, tgY = 0;

        function move() {
            curX += (tgX - curX) / 20;
            curY += (tgY - curY) / 20;
            interBubble.style.transform = `translate(${Math.round(curX)}px, ${Math.round(curY)}px)`;
            requestAnimationFrame(move);
        }

        widget.addEventListener('mousemove', e => {
            const rect = widget.getBoundingClientRect();
            tgX = e.clientX - rect.left - widget.offsetWidth / 2;
            tgY = e.clientY - rect.top - widget.offsetHeight / 2;
        });

        move();
    });
});
