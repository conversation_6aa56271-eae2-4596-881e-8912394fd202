<?php
/**
 * Plugin Name: Services Section for Elementor
 * Plugin URI: https://example.com/services-section-elementor
 * Description: Custom Elementor widget for responsive services section with hover effects. Compatible with TheGem theme.
 * Version: 1.0.0
 * Author: Your Name
 * Author URI: https://example.com
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: services-section-elementor
 * Domain Path: /languages
 * Requires Plugins: elementor
 * Elementor tested up to: 3.25.0
 * Elementor Pro tested up to: 3.25.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('SERVICES_SECTION_ELEMENTOR_VERSION', '1.0.0');
define('SERVICES_SECTION_ELEMENTOR_PLUGIN_URL', plugin_dir_url(__FILE__));
define('SERVICES_SECTION_ELEMENTOR_PLUGIN_PATH', plugin_dir_path(__FILE__));

/**
 * Main Plugin Class
 */
final class Services_Section_Elementor {

    /**
     * Plugin Version
     */
    const VERSION = '1.0.0';

    /**
     * Minimum Elementor Version
     */
    const MINIMUM_ELEMENTOR_VERSION = '3.0.0';

    /**
     * Minimum PHP Version
     */
    const MINIMUM_PHP_VERSION = '7.4';

    /**
     * Instance
     */
    private static $_instance = null;

    /**
     * Instance
     */
    public static function instance() {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    /**
     * Constructor
     */
    public function __construct() {
        add_action('init', [$this, 'i18n']);
        add_action('plugins_loaded', [$this, 'init']);
    }

    /**
     * Load Textdomain
     */
    public function i18n() {
        load_plugin_textdomain('services-section-elementor');
    }

    /**
     * Initialize the plugin
     */
    public function init() {
        // Check if Elementor installed and activated
        if (!did_action('elementor/loaded')) {
            add_action('admin_notices', [$this, 'admin_notice_missing_main_plugin']);
            return;
        }

        // Check for required Elementor version
        if (!version_compare(ELEMENTOR_VERSION, self::MINIMUM_ELEMENTOR_VERSION, '>=')) {
            add_action('admin_notices', [$this, 'admin_notice_minimum_elementor_version']);
            return;
        }

        // Check for required PHP version
        if (version_compare(PHP_VERSION, self::MINIMUM_PHP_VERSION, '<')) {
            add_action('admin_notices', [$this, 'admin_notice_minimum_php_version']);
            return;
        }

        // Add Plugin actions
        add_action('elementor/widgets/register', [$this, 'register_widgets']);
        add_action('elementor/frontend/after_enqueue_styles', [$this, 'widget_styles']);
        add_action('elementor/editor/before_enqueue_scripts', [$this, 'editor_scripts']);
        
        // TheGem theme compatibility
        add_action('wp_enqueue_scripts', [$this, 'thegem_compatibility'], 20);
    }

    /**
     * Admin notice - Missing main plugin
     */
    public function admin_notice_missing_main_plugin() {
        if (isset($_GET['activate'])) unset($_GET['activate']);
        $message = sprintf(
            esc_html__('"%1$s" requires "%2$s" to be installed and activated.', 'services-section-elementor'),
            '<strong>' . esc_html__('Services Section for Elementor', 'services-section-elementor') . '</strong>',
            '<strong>' . esc_html__('Elementor', 'services-section-elementor') . '</strong>'
        );
        printf('<div class="notice notice-warning is-dismissible"><p>%1$s</p></div>', $message);
    }

    /**
     * Admin notice - Minimum Elementor version
     */
    public function admin_notice_minimum_elementor_version() {
        if (isset($_GET['activate'])) unset($_GET['activate']);
        $message = sprintf(
            esc_html__('"%1$s" requires "%2$s" version %3$s or greater.', 'services-section-elementor'),
            '<strong>' . esc_html__('Services Section for Elementor', 'services-section-elementor') . '</strong>',
            '<strong>' . esc_html__('Elementor', 'services-section-elementor') . '</strong>',
            self::MINIMUM_ELEMENTOR_VERSION
        );
        printf('<div class="notice notice-warning is-dismissible"><p>%1$s</p></div>', $message);
    }

    /**
     * Admin notice - Minimum PHP version
     */
    public function admin_notice_minimum_php_version() {
        if (isset($_GET['activate'])) unset($_GET['activate']);
        $message = sprintf(
            esc_html__('"%1$s" requires "%2$s" version %3$s or greater.', 'services-section-elementor'),
            '<strong>' . esc_html__('Services Section for Elementor', 'services-section-elementor') . '</strong>',
            '<strong>' . esc_html__('PHP', 'services-section-elementor') . '</strong>',
            self::MINIMUM_PHP_VERSION
        );
        printf('<div class="notice notice-warning is-dismissible"><p>%1$s</p></div>', $message);
    }

    /**
     * Register Widgets
     */
    public function register_widgets($widgets_manager) {
        require_once(SERVICES_SECTION_ELEMENTOR_PLUGIN_PATH . 'widgets/services-section-widget.php');
        $widgets_manager->register(new \Services_Section_Widget());
    }

    /**
     * Widget Styles
     */
    public function widget_styles() {
        wp_enqueue_style(
            'services-section-elementor',
            SERVICES_SECTION_ELEMENTOR_PLUGIN_URL . 'assets/css/services-section.css',
            [],
            self::VERSION
        );
        
        // Enqueue Google Fonts
        wp_enqueue_style(
            'services-section-elementor-fonts',
            'https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400;1,400&display=swap',
            [],
            self::VERSION
        );
    }

    /**
     * Editor Scripts
     */
    public function editor_scripts() {
        wp_enqueue_script(
            'services-section-elementor-editor',
            SERVICES_SECTION_ELEMENTOR_PLUGIN_URL . 'assets/js/editor.js',
            ['elementor-editor'],
            self::VERSION,
            true
        );
    }

    /**
     * TheGem Theme Compatibility
     */
    public function thegem_compatibility() {
        // Check if TheGem theme is active
        $theme = wp_get_theme();
        if ('TheGem' === $theme->get('Name') || 'thegem' === $theme->get_template()) {
            wp_enqueue_style(
                'services-section-thegem-compat',
                SERVICES_SECTION_ELEMENTOR_PLUGIN_URL . 'assets/css/thegem-compatibility.css',
                ['thegem-main'],
                self::VERSION
            );
        }
    }
}

// Initialize the plugin
Services_Section_Elementor::instance();
