services-section-elementor/                                                                         0000755 0601751 0601001 00000000000 15042754471 014074  5                                                                                                    ustar   <PERSON>hmd                                                                                                                                                                                                                                                   services-section-elementor/assets/                                                                  0000755 0601751 0601001 00000000000 15042754425 015375  5                                                                                                    ustar   Mhmd                                                                                                                                                                                                                                                   services-section-elementor/assets/css/                                                              0000755 0601751 0601001 00000000000 15042754432 016163  5                                                                                                    ustar   Mhmd                                                                                                                                                                                                                                                   services-section-elementor/assets/css/index.php                                                     0000644 0601751 0601001 00000000034 15042754432 020000  0                                                                                                    ustar   Mhmd                                                                                                                                                                                                                                                   <?php
// Silence is golden.
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    services-section-elementor/assets/css/services-section.css                                          0000644 0601751 0601001 00000013364 15042754143 022170  0                                                                                                    ustar   Mhmd                                                                                                                                                                                                                                                   /**
 * Services Section Elementor Widget Styles
 * Version: 1.0.0
 */

/* Base styles */
.elementor-widget-services-section-commandnet * {
    box-sizing: border-box;
}

/* Services section container */
.services-section-commandnet {
    min-height: 100vh;
    background-color: #111827;
    text-align: center;
    padding: 5rem 2rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

/* Services subtitle */
.services-subtitle-commandnet {
    color: #9ca3af;
    font-size: 1.125rem;
    max-width: 32rem;
    margin: 0 auto 0.5rem auto;
    text-transform: capitalize;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Arrow icon */
.arrow-icon-commandnet {
    color: #4f46e5;
    margin-left: 0.75rem;
    width: 1.5rem;
    height: 1.5rem;
    flex-shrink: 0;
}

/* Services title */
.services-title-commandnet {
    color: white;
    font-size: 2.25rem;
    font-weight: 600;
    max-width: 48rem;
    margin: 0 auto 4rem auto;
    line-height: 1.375;
}

/* Grid container */
.grid-offer-commandnet {
    text-align: left;
    display: grid;
    gap: 1.25rem;
    max-width: 80rem;
    margin: 0 auto;
    grid-template-columns: 1fr;
}

/* Card base styles */
.card-commandnet {
    background-color: #1f2937;
    padding: 2.5rem;
    position: relative;
    overflow: hidden;
    border-radius: 0;
    transition: box-shadow 0.3s ease;
}

/* Card content positioning */
.card-content-commandnet {
    position: relative;
    z-index: 2;
}

.card-content-right-commandnet {
    padding-right: 0;
}

.card-content-left-commandnet {
    padding-left: 0;
}

/* Card title */
.card-title-commandnet {
    text-transform: capitalize;
    color: white;
    margin-bottom: 1rem;
    font-size: 1.5rem;
    font-family: "Playfair Display", serif;
    font-weight: 400;
    font-style: normal;
    line-height: 1.2;
}

/* Card description */
.card-description-commandnet {
    color: #9ca3af;
    transition: color 0.8s ease;
    line-height: 1.6;
}

/* Card hover effects */
.card-commandnet::before {
    position: absolute;
    content: "";
    width: 100%;
    height: 100%;
    transition: clip-path 0.6s ease;
    z-index: 1;
    background-color: #4f46e5;
    top: 0;
    left: 0;
}

.card-commandnet:hover {
    box-shadow: 0.063rem 0.063rem 1.25rem 0.375rem rgba(0, 0, 0, 0.53);
}

/* Specific clip-path positions for each card */
.card-commandnet:nth-child(1)::before {
    bottom: 0;
    right: 0;
    clip-path: circle(calc(6.25rem + 7.5vw) at 100% 100%);
}

.card-commandnet:nth-child(2)::before {
    bottom: 0;
    left: 0;
    clip-path: circle(calc(6.25rem + 7.5vw) at 0% 100%);
}

.card-commandnet:nth-child(3)::before {
    top: 0;
    right: 0;
    clip-path: circle(calc(6.25rem + 7.5vw) at 100% 0%);
}

.card-commandnet:nth-child(4)::before {
    top: 0;
    left: 0;
    clip-path: circle(calc(6.25rem + 7.5vw) at 0% 0%);
}

/* Hover animation */
.card-commandnet:hover::before {
    clip-path: circle(110vw at 100% 100%);
}

.card-commandnet:hover .card-description-commandnet {
    color: #fff;
}

/* Circle background images */
@media screen and (min-width: 62.5rem) {
    .circle-commandnet {
        position: absolute;
        width: 100%;
        height: 100%;
        z-index: 0;
        top: 0;
        left: 0;
    }
}

.card-commandnet:nth-child(1) .circle-commandnet {
    background: url("https://images.unsplash.com/photo-1587440871875-191322ee64b0?q=80&w=2071&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D") no-repeat 50% 50%/cover;
    bottom: 0;
    right: 0;
    clip-path: circle(calc(6.25rem + 7.5vw) at 100% 100%);
}

.card-commandnet:nth-child(2) .circle-commandnet {
    background: url("https://images.unsplash.com/photo-1499951360447-b19be8fe80f5?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D") no-repeat 50% 50%/cover;
    bottom: 0;
    left: 0;
    clip-path: circle(calc(6.25rem + 7.5vw) at 0% 100%);
}

.card-commandnet:nth-child(3) .circle-commandnet {
    background: url("https://images.unsplash.com/photo-1557804506-669a67965ba0?q=80&w=1974&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D") no-repeat 50% 50%/cover;
    top: 0;
    right: 0;
    clip-path: circle(calc(6.25rem + 7.5vw) at 100% 0%);
}

.card-commandnet:nth-child(4) .circle-commandnet {
    background: url("https://images.unsplash.com/photo-1600880292203-757bb62b4baf?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D") no-repeat 50% 50%/cover;
    top: 0;
    left: 0;
    clip-path: circle(calc(6.25rem + 7.5vw) at 0% 0%);
}

/* Responsive design */
@media (min-width: 640px) {
    .grid-offer-commandnet {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 768px) {
    .services-title-commandnet {
        font-size: 3rem;
    }
}

@media (min-width: 1024px) {
    .card-content-right-commandnet {
        padding-right: 13rem;
    }

    .card-content-left-commandnet {
        padding-left: 12rem;
    }

    .card-commandnet:nth-child(3) .card-content-right-commandnet {
        padding-right: 11rem;
    }
}

@media (min-width: 1280px) {
    .services-section-commandnet {
        padding-left: 0;
        padding-right: 0;
    }

    .services-title-commandnet {
        font-size: 3.75rem;
    }

    .card-title-commandnet {
        font-size: 1.875rem;
    }
}

/* Elementor editor specific styles */
.elementor-editor-active .card-commandnet::before {
    pointer-events: none;
}

/* Custom background image support */
.card-commandnet[data-bg-image] .circle-commandnet {
    background-size: cover !important;
    background-position: center !important;
    background-repeat: no-repeat !important;
}
                                                                                                                                                                                                                                                                            services-section-elementor/assets/css/thegem-compatibility.css                                      0000644 0601751 0601001 00000010364 15042754067 023025  0                                                                                                    ustar   Mhmd                                                                                                                                                                                                                                                   /**
 * TheGem Theme Compatibility Styles
 * Services Section for Elementor
 * Version: 1.0.0
 */

/* Reset TheGem's default styles that might conflict */
.elementor-widget-services-section-commandnet * {
    box-sizing: border-box !important;
}

/* Ensure TheGem's container styles don't interfere */
.elementor-widget-services-section-commandnet .services-section-commandnet {
    width: 100% !important;
    max-width: none !important;
    margin: 0 !important;
    padding: 5rem 2rem !important;
}

/* Override TheGem's typography settings */
.elementor-widget-services-section-commandnet .services-title-commandnet {
    font-family: inherit !important;
    line-height: 1.375 !important;
    letter-spacing: normal !important;
    text-transform: none !important;
}

.elementor-widget-services-section-commandnet .card-title-commandnet {
    font-family: "Playfair Display", serif !important;
    font-weight: 400 !important;
    line-height: 1.2 !important;
    letter-spacing: normal !important;
}

/* Ensure TheGem's grid system doesn't interfere */
.elementor-widget-services-section-commandnet .grid-offer-commandnet {
    display: grid !important;
    grid-template-columns: 1fr !important;
    gap: 1.25rem !important;
    width: 100% !important;
}

/* Override TheGem's button and link styles */
.elementor-widget-services-section-commandnet a,
.elementor-widget-services-section-commandnet button {
    text-decoration: none !important;
    border: none !important;
    background: transparent !important;
    box-shadow: none !important;
}

/* Ensure hover effects work with TheGem */
.elementor-widget-services-section-commandnet .card-commandnet {
    position: relative !important;
    overflow: hidden !important;
    transform: translateZ(0) !important; /* Force hardware acceleration */
}

.elementor-widget-services-section-commandnet .card-commandnet::before {
    position: absolute !important;
    content: "" !important;
    width: 100% !important;
    height: 100% !important;
    transition: all 0.6s ease !important;
    z-index: 0 !important;
    background-color: #4f46e5 !important;
    will-change: clip-path !important;
}

/* Responsive compatibility with TheGem's breakpoints */
@media (min-width: 640px) {
    .elementor-widget-services-section-commandnet .grid-offer-commandnet {
        grid-template-columns: repeat(2, 1fr) !important;
    }
}

@media (min-width: 1024px) {
    .elementor-widget-services-section-commandnet .services-section-commandnet {
        padding: 5rem 0 !important;
    }
}

/* Ensure TheGem's color scheme doesn't override our colors */
.elementor-widget-services-section-commandnet .services-section-commandnet {
    background-color: #111827 !important;
    color: inherit !important;
}

.elementor-widget-services-section-commandnet .services-subtitle-commandnet {
    color: #9ca3af !important;
}

.elementor-widget-services-section-commandnet .services-title-commandnet {
    color: white !important;
}

.elementor-widget-services-section-commandnet .card-commandnet {
    background-color: #1f2937 !important;
}

.elementor-widget-services-section-commandnet .card-title-commandnet {
    color: white !important;
}

.elementor-widget-services-section-commandnet .card-description-commandnet {
    color: #9ca3af !important;
}

/* Fix potential z-index conflicts with TheGem */
.elementor-widget-services-section-commandnet {
    position: relative !important;
    z-index: 1 !important;
}

.elementor-widget-services-section-commandnet .card-content-commandnet {
    position: relative !important;
    z-index: 2 !important;
}

/* Ensure smooth animations work with TheGem */
.elementor-widget-services-section-commandnet .card-commandnet:hover::before {
    clip-path: circle(110vw at 100% 100%) !important;
}

.elementor-widget-services-section-commandnet .card-commandnet:hover .card-description-commandnet {
    color: #fff !important;
}

/* TheGem specific fixes for Elementor editor */
.elementor-editor-active .elementor-widget-services-section-commandnet .card-commandnet::before {
    pointer-events: none !important;
}

/* Ensure proper spacing in TheGem containers */
.thegem-container .elementor-widget-services-section-commandnet,
.gem-container .elementor-widget-services-section-commandnet {
    width: 100% !important;
    max-width: none !important;
}
                                                                                                                                                                                                                                                                            services-section-elementor/assets/index.php                                                         0000644 0601751 0601001 00000000034 15042754425 017212  0                                                                                                    ustar   Mhmd                                                                                                                                                                                                                                                   <?php
// Silence is golden.
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    services-section-elementor/assets/js/                                                               0000755 0601751 0601001 00000000000 15042754441 016007  5                                                                                                    ustar   Mhmd                                                                                                                                                                                                                                                   services-section-elementor/assets/js/editor.js                                                      0000644 0601751 0601001 00000003651 15042754164 017642  0                                                                                                    ustar   Mhmd                                                                                                                                                                                                                                                   /**
 * Services Section Elementor Widget - Editor Scripts
 * Version: 1.0.0
 */

(function($) {
    'use strict';

    // Wait for Elementor to be ready
    $(window).on('elementor:init', function() {
        
        // Add custom CSS for better editor experience
        elementor.hooks.addAction('panel/open_editor/widget/services-section-commandnet', function(panel, model, view) {
            // Add custom styling to the editor panel if needed
            console.log('Services Section widget panel opened');
        });

        // Handle live preview updates
        elementor.channels.editor.on('change', function(controlView, elementView) {
            if (elementView.model.get('widgetType') === 'services-section-commandnet') {
                // Trigger re-render for live preview
                elementView.renderHTML();
            }
        });

        // Custom preview handling for image controls
        elementor.hooks.addAction('frontend/element_ready/services-section-commandnet.default', function($scope) {
            // Handle custom background images
            $scope.find('.card-commandnet').each(function(index) {
                var $card = $(this);
                var $circle = $card.find('.circle-commandnet');
                var bgImage = $card.data('bg-image');
                
                if (bgImage) {
                    $circle.css('background-image', 'url(' + bgImage + ')');
                }
            });
        });

    });

    // Helper function for image preview in editor
    window.servicesSection = {
        updateCardBackground: function(cardIndex, imageUrl) {
            var $card = $('.card-commandnet').eq(cardIndex);
            var $circle = $card.find('.circle-commandnet');
            
            if (imageUrl) {
                $circle.css('background-image', 'url(' + imageUrl + ')');
                $card.attr('data-bg-image', imageUrl);
            }
        }
    };

})(jQuery);
                                                                                       services-section-elementor/assets/js/index.php                                                      0000644 0601751 0601001 00000000034 15042754441 017624  0                                                                                                    ustar   Mhmd                                                                                                                                                                                                                                                   <?php
// Silence is golden.
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    services-section-elementor/index.php                                                                0000644 0601751 0601001 00000000034 15042754412 015704  0                                                                                                    ustar   Mhmd                                                                                                                                                                                                                                                   <?php
// Silence is golden.
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    services-section-elementor/readme.txt                                                               0000644 0601751 0601001 00000007402 15042754471 016075  0                                                                                                    ustar   Mhmd                                                                                                                                                                                                                                                   === Services Section for Elementor ===
Contributors: yourname
Tags: elementor, services, section, responsive, hover effects, thegem
Requires at least: 5.0
Tested up to: 6.4
Requires PHP: 7.4
Stable tag: 1.0.0
License: GPLv2 or later
License URI: https://www.gnu.org/licenses/gpl-2.0.html
Elementor tested up to: 3.25.0
Elementor Pro tested up to: 3.25.0

Custom Elementor widget for responsive services section with hover effects and animations. Fully compatible with TheGem theme.

== Description ==

Services Section for Elementor is a powerful WordPress plugin that adds a custom Elementor widget for creating stunning services sections with hover effects and animations.

**Key Features:**

* **Custom Elementor Widget** - Seamlessly integrates with Elementor page builder
* **TheGem Theme Compatible** - Specifically optimized for TheGem theme
* **Fully Customizable** - Control every aspect through Elementor's interface
* **Image Controls** - Easy background image management for each service card
* **Text Controls** - Editable titles and descriptions for all content
* **Live Preview** - See changes instantly in Elementor editor
* **Responsive Design** - Works perfectly on all devices
* **Hover Effects** - Beautiful clip-path animations on hover
* **Professional Styling** - Modern dark theme with elegant typography

**What You Can Customize:**

* Main title and subtitle
* Background images for each of the 4 service cards
* Service titles and descriptions
* Colors (background, text, hover overlay)
* Typography settings
* Spacing and padding
* All styling options through Elementor's interface

**Perfect For:**

* Business websites
* Service providers
* Agencies
* Portfolios
* Corporate sites
* Any site using TheGem theme + Elementor

== Installation ==

**Requirements:**
* WordPress 5.0 or higher
* PHP 7.4 or higher
* Elementor (free version) installed and activated

**Installation Steps:**

1. Upload the plugin files to `/wp-content/plugins/services-section-elementor/` directory
2. Activate the plugin through the 'Plugins' screen in WordPress
3. Edit any page with Elementor
4. Search for "Services Section" in the Elementor widgets panel
5. Drag and drop the widget to your page
6. Customize all settings in the left panel
7. Publish your page

== Usage ==

1. **Add the Widget**: In Elementor editor, search for "Services Section" and drag it to your page
2. **Customize Content**: Use the Content tab to edit titles, descriptions, and upload images
3. **Style Your Section**: Use the Style tab to customize colors, typography, and spacing
4. **Preview Changes**: All changes show instantly in the editor
5. **Publish**: Save and publish your page

== Frequently Asked Questions ==

= Does this work with any theme? =

While it works with most themes, it's specifically optimized for TheGem theme with special compatibility CSS included.

= Can I change the background images? =

Yes! Each service card has its own image control in the Elementor editor. Simply click and upload your own images.

= Is it responsive? =

Absolutely! The widget is fully responsive and looks great on all devices.

= Do I need Elementor Pro? =

No, this widget works with the free version of Elementor.

= Can I customize the colors? =

Yes, you have full control over all colors including background, text, and hover overlay colors.

== Screenshots ==

1. Services section on frontend with hover effects
2. Elementor editor with widget controls
3. Image upload interface
4. Style customization options
5. Responsive design on mobile

== Changelog ==

= 1.0.0 =
* Initial release
* Custom Elementor widget implementation
* TheGem theme compatibility
* Full customization controls
* Responsive design
* Hover animations
* Live preview support

== Upgrade Notice ==

= 1.0.0 =
Initial release of Services Section for Elementor plugin.
                                                                                                                                                                                                                                                              services-section-elementor/services-section-elementor.php                                           0000644 0601751 0601001 00000015147 15042754030 022061  0                                                                                                    ustar   Mhmd                                                                                                                                                                                                                                                   <?php
/**
 * Plugin Name: Services Section for Elementor
 * Plugin URI: https://example.com/services-section-elementor
 * Description: Custom Elementor widget for responsive services section with hover effects. Compatible with TheGem theme.
 * Version: 1.0.0
 * Author: Your Name
 * Author URI: https://example.com
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: services-section-elementor
 * Domain Path: /languages
 * Requires Plugins: elementor
 * Elementor tested up to: 3.25.0
 * Elementor Pro tested up to: 3.25.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('SERVICES_SECTION_ELEMENTOR_VERSION', '1.0.0');
define('SERVICES_SECTION_ELEMENTOR_PLUGIN_URL', plugin_dir_url(__FILE__));
define('SERVICES_SECTION_ELEMENTOR_PLUGIN_PATH', plugin_dir_path(__FILE__));

/**
 * Main Plugin Class
 */
final class Services_Section_Elementor {

    /**
     * Plugin Version
     */
    const VERSION = '1.0.0';

    /**
     * Minimum Elementor Version
     */
    const MINIMUM_ELEMENTOR_VERSION = '3.0.0';

    /**
     * Minimum PHP Version
     */
    const MINIMUM_PHP_VERSION = '7.4';

    /**
     * Instance
     */
    private static $_instance = null;

    /**
     * Instance
     */
    public static function instance() {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    /**
     * Constructor
     */
    public function __construct() {
        add_action('init', [$this, 'i18n']);
        add_action('plugins_loaded', [$this, 'init']);
    }

    /**
     * Load Textdomain
     */
    public function i18n() {
        load_plugin_textdomain('services-section-elementor');
    }

    /**
     * Initialize the plugin
     */
    public function init() {
        // Check if Elementor installed and activated
        if (!did_action('elementor/loaded')) {
            add_action('admin_notices', [$this, 'admin_notice_missing_main_plugin']);
            return;
        }

        // Check for required Elementor version
        if (!version_compare(ELEMENTOR_VERSION, self::MINIMUM_ELEMENTOR_VERSION, '>=')) {
            add_action('admin_notices', [$this, 'admin_notice_minimum_elementor_version']);
            return;
        }

        // Check for required PHP version
        if (version_compare(PHP_VERSION, self::MINIMUM_PHP_VERSION, '<')) {
            add_action('admin_notices', [$this, 'admin_notice_minimum_php_version']);
            return;
        }

        // Add Plugin actions
        add_action('elementor/widgets/register', [$this, 'register_widgets']);
        add_action('elementor/frontend/after_enqueue_styles', [$this, 'widget_styles']);
        add_action('elementor/editor/before_enqueue_scripts', [$this, 'editor_scripts']);
        
        // TheGem theme compatibility
        add_action('wp_enqueue_scripts', [$this, 'thegem_compatibility'], 20);
    }

    /**
     * Admin notice - Missing main plugin
     */
    public function admin_notice_missing_main_plugin() {
        if (isset($_GET['activate'])) unset($_GET['activate']);
        $message = sprintf(
            esc_html__('"%1$s" requires "%2$s" to be installed and activated.', 'services-section-elementor'),
            '<strong>' . esc_html__('Services Section for Elementor', 'services-section-elementor') . '</strong>',
            '<strong>' . esc_html__('Elementor', 'services-section-elementor') . '</strong>'
        );
        printf('<div class="notice notice-warning is-dismissible"><p>%1$s</p></div>', $message);
    }

    /**
     * Admin notice - Minimum Elementor version
     */
    public function admin_notice_minimum_elementor_version() {
        if (isset($_GET['activate'])) unset($_GET['activate']);
        $message = sprintf(
            esc_html__('"%1$s" requires "%2$s" version %3$s or greater.', 'services-section-elementor'),
            '<strong>' . esc_html__('Services Section for Elementor', 'services-section-elementor') . '</strong>',
            '<strong>' . esc_html__('Elementor', 'services-section-elementor') . '</strong>',
            self::MINIMUM_ELEMENTOR_VERSION
        );
        printf('<div class="notice notice-warning is-dismissible"><p>%1$s</p></div>', $message);
    }

    /**
     * Admin notice - Minimum PHP version
     */
    public function admin_notice_minimum_php_version() {
        if (isset($_GET['activate'])) unset($_GET['activate']);
        $message = sprintf(
            esc_html__('"%1$s" requires "%2$s" version %3$s or greater.', 'services-section-elementor'),
            '<strong>' . esc_html__('Services Section for Elementor', 'services-section-elementor') . '</strong>',
            '<strong>' . esc_html__('PHP', 'services-section-elementor') . '</strong>',
            self::MINIMUM_PHP_VERSION
        );
        printf('<div class="notice notice-warning is-dismissible"><p>%1$s</p></div>', $message);
    }

    /**
     * Register Widgets
     */
    public function register_widgets($widgets_manager) {
        require_once(SERVICES_SECTION_ELEMENTOR_PLUGIN_PATH . 'widgets/services-section-widget.php');
        $widgets_manager->register(new \Services_Section_Widget());
    }

    /**
     * Widget Styles
     */
    public function widget_styles() {
        wp_enqueue_style(
            'services-section-elementor',
            SERVICES_SECTION_ELEMENTOR_PLUGIN_URL . 'assets/css/services-section.css',
            [],
            self::VERSION
        );
        
        // Enqueue Google Fonts
        wp_enqueue_style(
            'services-section-elementor-fonts',
            'https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400;1,400&display=swap',
            [],
            self::VERSION
        );
    }

    /**
     * Editor Scripts
     */
    public function editor_scripts() {
        wp_enqueue_script(
            'services-section-elementor-editor',
            SERVICES_SECTION_ELEMENTOR_PLUGIN_URL . 'assets/js/editor.js',
            ['elementor-editor'],
            self::VERSION,
            true
        );
    }

    /**
     * TheGem Theme Compatibility
     */
    public function thegem_compatibility() {
        // Check if TheGem theme is active
        $theme = wp_get_theme();
        if ('TheGem' === $theme->get('Name') || 'thegem' === $theme->get_template()) {
            wp_enqueue_style(
                'services-section-thegem-compat',
                SERVICES_SECTION_ELEMENTOR_PLUGIN_URL . 'assets/css/thegem-compatibility.css',
                ['thegem-main'],
                self::VERSION
            );
        }
    }
}

// Initialize the plugin
Services_Section_Elementor::instance();
                                                                                                                                                                                                                                                                                                                                                                                                                         services-section-elementor/widgets/                                                                 0000755 0601751 0601001 00000000000 15042754417 015542  5                                                                                                    ustar   Mhmd                                                                                                                                                                                                                                                   services-section-elementor/widgets/index.php                                                        0000644 0601751 0601001 00000000034 15042754417 017357  0                                                                                                    ustar   Mhmd                                                                                                                                                                                                                                                   <?php
// Silence is golden.
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    services-section-elementor/widgets/services-section-widget.php                                      0000644 0601751 0601001 00000050127 15042754366 023031  0                                                                                                    ustar   Mhmd                                                                                                                                                                                                                                                   <?php
/**
 * Services Section Elementor Widget
 * 
 * @package Services_Section_Elementor
 * @version 1.0.0
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

/**
 * Services Section Widget Class
 */
class Services_Section_Widget extends \Elementor\Widget_Base {

    /**
     * Get widget name.
     */
    public function get_name() {
        return 'services-section-commandnet';
    }

    /**
     * Get widget title.
     */
    public function get_title() {
        return esc_html__('Services Section', 'services-section-elementor');
    }

    /**
     * Get widget icon.
     */
    public function get_icon() {
        return 'eicon-gallery-grid';
    }

    /**
     * Get widget categories.
     */
    public function get_categories() {
        return ['general'];
    }

    /**
     * Get widget keywords.
     */
    public function get_keywords() {
        return ['services', 'section', 'cards', 'hover', 'business'];
    }

    /**
     * Register widget controls.
     */
    protected function register_controls() {

        // Content Tab - Main Settings
        $this->start_controls_section(
            'content_section',
            [
                'label' => esc_html__('Main Content', 'services-section-elementor'),
                'tab' => \Elementor\Controls_Manager::TAB_CONTENT,
            ]
        );

        $this->add_control(
            'subtitle',
            [
                'label' => esc_html__('Subtitle', 'services-section-elementor'),
                'type' => \Elementor\Controls_Manager::TEXT,
                'default' => esc_html__("what we're offering", 'services-section-elementor'),
                'placeholder' => esc_html__('Enter subtitle', 'services-section-elementor'),
            ]
        );

        $this->add_control(
            'title',
            [
                'label' => esc_html__('Main Title', 'services-section-elementor'),
                'type' => \Elementor\Controls_Manager::TEXTAREA,
                'default' => esc_html__('Services Built Specifically for your Business', 'services-section-elementor'),
                'placeholder' => esc_html__('Enter main title', 'services-section-elementor'),
                'rows' => 3,
            ]
        );

        $this->end_controls_section();

        // Content Tab - Service Cards
        $this->start_controls_section(
            'services_section',
            [
                'label' => esc_html__('Service Cards', 'services-section-elementor'),
                'tab' => \Elementor\Controls_Manager::TAB_CONTENT,
            ]
        );

        // Service 1
        $this->add_control(
            'service_1_heading',
            [
                'label' => esc_html__('Service 1', 'services-section-elementor'),
                'type' => \Elementor\Controls_Manager::HEADING,
                'separator' => 'before',
            ]
        );

        $this->add_control(
            'service_1_image',
            [
                'label' => esc_html__('Background Image', 'services-section-elementor'),
                'type' => \Elementor\Controls_Manager::MEDIA,
                'default' => [
                    'url' => 'https://images.unsplash.com/photo-1587440871875-191322ee64b0?q=80&w=2071&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
                ],
            ]
        );

        $this->add_control(
            'service_1_title',
            [
                'label' => esc_html__('Title', 'services-section-elementor'),
                'type' => \Elementor\Controls_Manager::TEXTAREA,
                'default' => esc_html__("UI/UX\ncreative design", 'services-section-elementor'),
                'rows' => 2,
            ]
        );

        $this->add_control(
            'service_1_description',
            [
                'label' => esc_html__('Description', 'services-section-elementor'),
                'type' => \Elementor\Controls_Manager::TEXTAREA,
                'default' => esc_html__('Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Pellentesque habitant morbi tristique senectus et netus et malesuada fames.', 'services-section-elementor'),
                'rows' => 3,
            ]
        );

        // Service 2
        $this->add_control(
            'service_2_heading',
            [
                'label' => esc_html__('Service 2', 'services-section-elementor'),
                'type' => \Elementor\Controls_Manager::HEADING,
                'separator' => 'before',
            ]
        );

        $this->add_control(
            'service_2_image',
            [
                'label' => esc_html__('Background Image', 'services-section-elementor'),
                'type' => \Elementor\Controls_Manager::MEDIA,
                'default' => [
                    'url' => 'https://images.unsplash.com/photo-1499951360447-b19be8fe80f5?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
                ],
            ]
        );

        $this->add_control(
            'service_2_title',
            [
                'label' => esc_html__('Title', 'services-section-elementor'),
                'type' => \Elementor\Controls_Manager::TEXTAREA,
                'default' => esc_html__("visual\ngraphic design", 'services-section-elementor'),
                'rows' => 2,
            ]
        );

        $this->add_control(
            'service_2_description',
            [
                'label' => esc_html__('Description', 'services-section-elementor'),
                'type' => \Elementor\Controls_Manager::TEXTAREA,
                'default' => esc_html__('Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Pellentesque habitant morbi tristique senectus et netus et malesuada fames.', 'services-section-elementor'),
                'rows' => 3,
            ]
        );

        // Service 3
        $this->add_control(
            'service_3_heading',
            [
                'label' => esc_html__('Service 3', 'services-section-elementor'),
                'type' => \Elementor\Controls_Manager::HEADING,
                'separator' => 'before',
            ]
        );

        $this->add_control(
            'service_3_image',
            [
                'label' => esc_html__('Background Image', 'services-section-elementor'),
                'type' => \Elementor\Controls_Manager::MEDIA,
                'default' => [
                    'url' => 'https://images.unsplash.com/photo-1557804506-669a67965ba0?q=80&w=1974&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
                ],
            ]
        );

        $this->add_control(
            'service_3_title',
            [
                'label' => esc_html__('Title', 'services-section-elementor'),
                'type' => \Elementor\Controls_Manager::TEXTAREA,
                'default' => esc_html__("strategy &\ndigital marketing", 'services-section-elementor'),
                'rows' => 2,
            ]
        );

        $this->add_control(
            'service_3_description',
            [
                'label' => esc_html__('Description', 'services-section-elementor'),
                'type' => \Elementor\Controls_Manager::TEXTAREA,
                'default' => esc_html__('Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Pellentesque habitant morbi tristique senectus et netus et malesuada fames.', 'services-section-elementor'),
                'rows' => 3,
            ]
        );

        // Service 4
        $this->add_control(
            'service_4_heading',
            [
                'label' => esc_html__('Service 4', 'services-section-elementor'),
                'type' => \Elementor\Controls_Manager::HEADING,
                'separator' => 'before',
            ]
        );

        $this->add_control(
            'service_4_image',
            [
                'label' => esc_html__('Background Image', 'services-section-elementor'),
                'type' => \Elementor\Controls_Manager::MEDIA,
                'default' => [
                    'url' => 'https://images.unsplash.com/photo-1600880292203-757bb62b4baf?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
                ],
            ]
        );

        $this->add_control(
            'service_4_title',
            [
                'label' => esc_html__('Title', 'services-section-elementor'),
                'type' => \Elementor\Controls_Manager::TEXTAREA,
                'default' => esc_html__("effective\nbusiness growth", 'services-section-elementor'),
                'rows' => 2,
            ]
        );

        $this->add_control(
            'service_4_description',
            [
                'label' => esc_html__('Description', 'services-section-elementor'),
                'type' => \Elementor\Controls_Manager::TEXTAREA,
                'default' => esc_html__('Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Pellentesque habitant morbi tristique senectus et netus et malesuada fames.', 'services-section-elementor'),
                'rows' => 3,
            ]
        );

        $this->end_controls_section();

        // Style Tab - General Styling
        $this->start_controls_section(
            'style_general',
            [
                'label' => esc_html__('General', 'services-section-elementor'),
                'tab' => \Elementor\Controls_Manager::TAB_STYLE,
            ]
        );

        $this->add_control(
            'section_background',
            [
                'label' => esc_html__('Section Background', 'services-section-elementor'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#111827',
                'selectors' => [
                    '{{WRAPPER}} .services-section-commandnet' => 'background-color: {{VALUE}};',
                ],
            ]
        );

        $this->add_responsive_control(
            'section_padding',
            [
                'label' => esc_html__('Section Padding', 'services-section-elementor'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', 'em', '%'],
                'default' => [
                    'top' => '80',
                    'right' => '32',
                    'bottom' => '80',
                    'left' => '32',
                    'unit' => 'px',
                ],
                'selectors' => [
                    '{{WRAPPER}} .services-section-commandnet' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );

        $this->end_controls_section();

        // Style Tab - Typography
        $this->start_controls_section(
            'style_typography',
            [
                'label' => esc_html__('Typography', 'services-section-elementor'),
                'tab' => \Elementor\Controls_Manager::TAB_STYLE,
            ]
        );

        $this->add_control(
            'subtitle_color',
            [
                'label' => esc_html__('Subtitle Color', 'services-section-elementor'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#9ca3af',
                'selectors' => [
                    '{{WRAPPER}} .services-subtitle-commandnet' => 'color: {{VALUE}};',
                ],
            ]
        );

        $this->add_group_control(
            \Elementor\Group_Control_Typography::get_type(),
            [
                'name' => 'subtitle_typography',
                'label' => esc_html__('Subtitle Typography', 'services-section-elementor'),
                'selector' => '{{WRAPPER}} .services-subtitle-commandnet',
            ]
        );

        $this->add_control(
            'title_color',
            [
                'label' => esc_html__('Main Title Color', 'services-section-elementor'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#ffffff',
                'selectors' => [
                    '{{WRAPPER}} .services-title-commandnet' => 'color: {{VALUE}};',
                ],
            ]
        );

        $this->add_group_control(
            \Elementor\Group_Control_Typography::get_type(),
            [
                'name' => 'title_typography',
                'label' => esc_html__('Main Title Typography', 'services-section-elementor'),
                'selector' => '{{WRAPPER}} .services-title-commandnet',
            ]
        );

        $this->end_controls_section();

        // Style Tab - Cards
        $this->start_controls_section(
            'style_cards',
            [
                'label' => esc_html__('Service Cards', 'services-section-elementor'),
                'tab' => \Elementor\Controls_Manager::TAB_STYLE,
            ]
        );

        $this->add_control(
            'card_background',
            [
                'label' => esc_html__('Card Background', 'services-section-elementor'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#1f2937',
                'selectors' => [
                    '{{WRAPPER}} .card-commandnet' => 'background-color: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'card_title_color',
            [
                'label' => esc_html__('Card Title Color', 'services-section-elementor'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#ffffff',
                'selectors' => [
                    '{{WRAPPER}} .card-title-commandnet' => 'color: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'card_description_color',
            [
                'label' => esc_html__('Card Description Color', 'services-section-elementor'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#9ca3af',
                'selectors' => [
                    '{{WRAPPER}} .card-description-commandnet' => 'color: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'hover_overlay_color',
            [
                'label' => esc_html__('Hover Overlay Color', 'services-section-elementor'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#4f46e5',
                'selectors' => [
                    '{{WRAPPER}} .card-commandnet::before' => 'background-color: {{VALUE}};',
                ],
            ]
        );

        $this->end_controls_section();
    }

    /**
     * Render widget output on the frontend.
     */
    protected function render() {
        $settings = $this->get_settings_for_display();

        // Prepare service data
        $services = [
            [
                'image' => $settings['service_1_image']['url'] ?? '',
                'title' => $settings['service_1_title'] ?? '',
                'description' => $settings['service_1_description'] ?? '',
                'position' => 'right'
            ],
            [
                'image' => $settings['service_2_image']['url'] ?? '',
                'title' => $settings['service_2_title'] ?? '',
                'description' => $settings['service_2_description'] ?? '',
                'position' => 'left'
            ],
            [
                'image' => $settings['service_3_image']['url'] ?? '',
                'title' => $settings['service_3_title'] ?? '',
                'description' => $settings['service_3_description'] ?? '',
                'position' => 'right'
            ],
            [
                'image' => $settings['service_4_image']['url'] ?? '',
                'title' => $settings['service_4_title'] ?? '',
                'description' => $settings['service_4_description'] ?? '',
                'position' => 'left'
            ]
        ];
        ?>
        <section class="services-section-commandnet">
            <span class="services-subtitle-commandnet">
                <?php echo esc_html($settings['subtitle']); ?>
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="arrow-icon-commandnet">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M17.25 8.25 21 12m0 0-3.75 3.75M21 12H3" />
                </svg>
            </span>

            <h1 class="services-title-commandnet">
                <?php echo wp_kses_post(nl2br($settings['title'])); ?>
            </h1>

            <div class="grid-offer-commandnet">
                <?php foreach ($services as $index => $service) : ?>
                    <div class="card-commandnet" <?php if ($service['image']) echo 'data-bg-image="' . esc_url($service['image']) . '"'; ?>>
                        <div class="circle-commandnet" <?php if ($service['image']) echo 'style="background-image: url(' . esc_url($service['image']) . ');"'; ?>></div>
                        <div class="card-content-commandnet card-content-<?php echo esc_attr($service['position']); ?>-commandnet">
                            <h2 class="card-title-commandnet">
                                <?php echo wp_kses_post(nl2br($service['title'])); ?>
                            </h2>
                            <p class="card-description-commandnet">
                                <?php echo esc_html($service['description']); ?>
                            </p>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </section>
        <?php
    }

    /**
     * Render widget output in the editor.
     */
    protected function content_template() {
        ?>
        <#
        var services = [
            {
                image: settings.service_1_image.url || '',
                title: settings.service_1_title || '',
                description: settings.service_1_description || '',
                position: 'right'
            },
            {
                image: settings.service_2_image.url || '',
                title: settings.service_2_title || '',
                description: settings.service_2_description || '',
                position: 'left'
            },
            {
                image: settings.service_3_image.url || '',
                title: settings.service_3_title || '',
                description: settings.service_3_description || '',
                position: 'right'
            },
            {
                image: settings.service_4_image.url || '',
                title: settings.service_4_title || '',
                description: settings.service_4_description || '',
                position: 'left'
            }
        ];
        #>
        <section class="services-section-commandnet">
            <span class="services-subtitle-commandnet">
                {{{ settings.subtitle }}}
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="arrow-icon-commandnet">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M17.25 8.25 21 12m0 0-3.75 3.75M21 12H3" />
                </svg>
            </span>

            <h1 class="services-title-commandnet">
                {{{ settings.title.replace(/\n/g, '<br>') }}}
            </h1>

            <div class="grid-offer-commandnet">
                <# _.each(services, function(service, index) { #>
                    <div class="card-commandnet" <# if (service.image) { #>data-bg-image="{{{ service.image }}}"<# } #>>
                        <div class="circle-commandnet" <# if (service.image) { #>style="background-image: url({{{ service.image }}});"<# } #>></div>
                        <div class="card-content-commandnet card-content-{{{ service.position }}}-commandnet">
                            <h2 class="card-title-commandnet">
                                {{{ service.title.replace(/\n/g, '<br>') }}}
                            </h2>
                            <p class="card-description-commandnet">
                                {{{ service.description }}}
                            </p>
                        </div>
                    </div>
                <# }); #>
            </div>
        </section>
        <?php
    }
}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         