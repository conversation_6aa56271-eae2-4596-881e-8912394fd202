<?php
/**
 * Plugin Name: Services Section for Elementor
 * Plugin URI: https://commandnet.co/services-section-elementor
 * Description: Custom Elementor widget for responsive services section with hover effects. Compatible with TheGem theme.
 * Version: 1.0.0
 * Author: CommandNet Solutions
 * Author URI: https://commandnet.co
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: services-section-elementor
 * Domain Path: /languages
 * Requires Plugins: elementor
 * Elementor tested up to: 3.25.0
 * Elementor Pro tested up to: 3.25.0
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * Network: false
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('SERVICES_SECTION_ELEMENTOR_VERSION', '1.0.0');
define('SERVICES_SECTION_ELEMENTOR_PLUGIN_URL', plugin_dir_url(__FILE__));
define('SERVICES_SECTION_ELEMENTOR_PLUGIN_PATH', plugin_dir_path(__FILE__));

/**
 * Main Plugin Class
 */
final class Services_Section_Elementor {

    /**
     * Plugin Version
     */
    const VERSION = '1.0.0';

    /**
     * Minimum Elementor Version
     */
    const MINIMUM_ELEMENTOR_VERSION = '3.0.0';

    /**
     * Minimum PHP Version
     */
    const MINIMUM_PHP_VERSION = '7.4';

    /**
     * Instance
     */
    private static $_instance = null;

    /**
     * Instance
     */
    public static function instance() {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    /**
     * Constructor
     */
    public function __construct() {
        add_action('init', [$this, 'i18n']);
        add_action('plugins_loaded', [$this, 'init']);
    }

    /**
     * Load Textdomain
     */
    public function i18n() {
        load_plugin_textdomain('services-section-elementor');
    }

    /**
     * Initialize the plugin
     */
    public function init() {
        // Check if Elementor installed and activated
        if (!did_action('elementor/loaded')) {
            add_action('admin_notices', [$this, 'admin_notice_missing_main_plugin']);
            return;
        }

        // Check for required Elementor version
        if (!version_compare(ELEMENTOR_VERSION, self::MINIMUM_ELEMENTOR_VERSION, '>=')) {
            add_action('admin_notices', [$this, 'admin_notice_minimum_elementor_version']);
            return;
        }

        // Check for required PHP version
        if (version_compare(PHP_VERSION, self::MINIMUM_PHP_VERSION, '<')) {
            add_action('admin_notices', [$this, 'admin_notice_minimum_php_version']);
            return;
        }

        // Add Plugin actions
        add_action('elementor/widgets/register', [$this, 'register_widgets']);
        add_action('elementor/frontend/after_enqueue_styles', [$this, 'widget_styles']);
        add_action('elementor/editor/before_enqueue_scripts', [$this, 'editor_scripts']);
        
        // TheGem theme compatibility
        add_action('wp_enqueue_scripts', [$this, 'thegem_compatibility'], 20);
    }

    /**
     * Admin notice - Missing main plugin
     */
    public function admin_notice_missing_main_plugin() {
        if (isset($_GET['activate'])) unset($_GET['activate']);
        $message = sprintf(
            esc_html__('"%1$s" requires "%2$s" to be installed and activated.', 'services-section-elementor'),
            '<strong>' . esc_html__('Services Section for Elementor', 'services-section-elementor') . '</strong>',
            '<strong>' . esc_html__('Elementor', 'services-section-elementor') . '</strong>'
        );
        printf('<div class="notice notice-warning is-dismissible"><p>%1$s</p></div>', $message);
    }

    /**
     * Admin notice - Minimum Elementor version
     */
    public function admin_notice_minimum_elementor_version() {
        if (isset($_GET['activate'])) unset($_GET['activate']);
        $message = sprintf(
            esc_html__('"%1$s" requires "%2$s" version %3$s or greater.', 'services-section-elementor'),
            '<strong>' . esc_html__('Services Section for Elementor', 'services-section-elementor') . '</strong>',
            '<strong>' . esc_html__('Elementor', 'services-section-elementor') . '</strong>',
            self::MINIMUM_ELEMENTOR_VERSION
        );
        printf('<div class="notice notice-warning is-dismissible"><p>%1$s</p></div>', $message);
    }

    /**
     * Admin notice - Minimum PHP version
     */
    public function admin_notice_minimum_php_version() {
        if (isset($_GET['activate'])) unset($_GET['activate']);
        $message = sprintf(
            esc_html__('"%1$s" requires "%2$s" version %3$s or greater.', 'services-section-elementor'),
            '<strong>' . esc_html__('Services Section for Elementor', 'services-section-elementor') . '</strong>',
            '<strong>' . esc_html__('PHP', 'services-section-elementor') . '</strong>',
            self::MINIMUM_PHP_VERSION
        );
        printf('<div class="notice notice-warning is-dismissible"><p>%1$s</p></div>', $message);
    }

    /**
     * Register Widgets
     */
    public function register_widgets($widgets_manager) {
        $widgets_manager->register(new Services_Section_Widget());
    }

    /**
     * Widget Styles
     */
    public function widget_styles() {
        // CSS file path
        $css_file = SERVICES_SECTION_ELEMENTOR_PLUGIN_PATH . 'services-section-elementor.css';
        $css_url = SERVICES_SECTION_ELEMENTOR_PLUGIN_URL . 'services-section-elementor.css';

        // Debug information (remove in production)
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('Services Section Plugin Path: ' . SERVICES_SECTION_ELEMENTOR_PLUGIN_PATH);
            error_log('Services Section Plugin URL: ' . SERVICES_SECTION_ELEMENTOR_PLUGIN_URL);
            error_log('CSS file exists: ' . (file_exists($css_file) ? 'Yes' : 'No'));
        }

        // Enqueue CSS
        if (file_exists($css_file)) {
            wp_enqueue_style(
                'services-section-elementor',
                $css_url,
                [],
                self::VERSION
            );
        } else {
            error_log('Services Section Elementor: CSS file not found at ' . $css_file);
        }
        
        // Enqueue Google Fonts
        wp_enqueue_style(
            'services-section-elementor-fonts',
            'https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400;1,400&display=swap',
            [],
            self::VERSION
        );
    }

    /**
     * Editor Scripts
     */
    public function editor_scripts() {
        // JavaScript file path
        $js_file = SERVICES_SECTION_ELEMENTOR_PLUGIN_PATH . 'services-section-elementor.js';
        $js_url = SERVICES_SECTION_ELEMENTOR_PLUGIN_URL . 'services-section-elementor.js';

        // Debug information (remove in production)
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('JS file exists: ' . (file_exists($js_file) ? 'Yes' : 'No'));
        }

        // Enqueue JavaScript
        if (file_exists($js_file)) {
            wp_enqueue_script(
                'services-section-elementor-editor',
                $js_url,
                ['elementor-editor'],
                self::VERSION,
                true
            );
        } else {
            error_log('Services Section Elementor: JS file not found at ' . $js_file);
        }
    }

    /**
     * TheGem Theme Compatibility
     */
    public function thegem_compatibility() {
        // Check if TheGem theme is active
        $theme = wp_get_theme();
        if ('TheGem' === $theme->get('Name') || 'thegem' === $theme->get_template()) {
            // Add TheGem specific styles inline if needed
            wp_add_inline_style('services-section-elementor', '
                .thegem-body .services-section-commandnet {
                    position: relative;
                    z-index: 1;
                }
            ');
        }
    }

    /**
     * Plugin activation hook
     */
    public function activate_plugin() {
        // Log activation for debugging
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('Services Section Elementor plugin activated');
            error_log('Plugin directory: ' . SERVICES_SECTION_ELEMENTOR_PLUGIN_PATH);
            error_log('Plugin URL: ' . SERVICES_SECTION_ELEMENTOR_PLUGIN_URL);
        }
    }

    /**
     * Plugin deactivation hook
     */
    public function deactivate_plugin() {
        // Log deactivation for debugging
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('Services Section Elementor plugin deactivated');
        }
    }

    /**
     * Check if plugin files exist and show admin notice if not
     */
    public function check_plugin_files() {
        $css_file = SERVICES_SECTION_ELEMENTOR_PLUGIN_PATH . 'services-section-elementor.css';
        $js_file = SERVICES_SECTION_ELEMENTOR_PLUGIN_PATH . 'services-section-elementor.js';

        $missing_files = array();

        if (!file_exists($css_file)) {
            $missing_files[] = 'services-section-elementor.css';
        }

        if (!file_exists($js_file)) {
            $missing_files[] = 'services-section-elementor.js';
        }

        if (!empty($missing_files)) {
            add_action('admin_notices', function() use ($missing_files) {
                echo '<div class="notice notice-error"><p>';
                echo '<strong>Services Section Elementor:</strong> Missing files: ' . implode(', ', $missing_files);
                echo '<br>Plugin directory: ' . SERVICES_SECTION_ELEMENTOR_PLUGIN_PATH;
                echo '</p></div>';
            });
        }
    }
}

// Register activation and deactivation hooks
register_activation_hook(__FILE__, [Services_Section_Elementor::instance(), 'activate_plugin']);
register_deactivation_hook(__FILE__, [Services_Section_Elementor::instance(), 'deactivate_plugin']);

// Add file check on admin init
add_action('admin_init', [Services_Section_Elementor::instance(), 'check_plugin_files']);

/**
 * Services Section Widget Class
 */
class Services_Section_Widget extends \Elementor\Widget_Base {

    /**
     * Get widget name.
     */
    public function get_name() {
        return 'services-section-commandnet';
    }

    /**
     * Get widget title.
     */
    public function get_title() {
        return esc_html__('Services Section', 'services-section-elementor');
    }

    /**
     * Get widget icon.
     */
    public function get_icon() {
        return 'eicon-gallery-grid';
    }

    /**
     * Get widget categories.
     */
    public function get_categories() {
        return ['general'];
    }

    /**
     * Get widget keywords.
     */
    public function get_keywords() {
        return ['services', 'section', 'cards', 'hover', 'business'];
    }

    /**
     * Register widget controls.
     */
    protected function register_controls() {

        // Content Tab - Main Settings
        $this->start_controls_section(
            'content_section',
            [
                'label' => esc_html__('Main Content', 'services-section-elementor'),
                'tab' => \Elementor\Controls_Manager::TAB_CONTENT,
            ]
        );

        $this->add_control(
            'subtitle',
            [
                'label' => esc_html__('Subtitle', 'services-section-elementor'),
                'type' => \Elementor\Controls_Manager::TEXT,
                'default' => esc_html__("what we're offering", 'services-section-elementor'),
                'placeholder' => esc_html__('Enter subtitle', 'services-section-elementor'),
            ]
        );

        $this->add_control(
            'title',
            [
                'label' => esc_html__('Main Title', 'services-section-elementor'),
                'type' => \Elementor\Controls_Manager::TEXTAREA,
                'default' => esc_html__('Services Built Specifically for your Business', 'services-section-elementor'),
                'placeholder' => esc_html__('Enter main title', 'services-section-elementor'),
                'rows' => 3,
            ]
        );

        $this->end_controls_section();

        // Content Tab - Service Cards
        $this->start_controls_section(
            'services_section',
            [
                'label' => esc_html__('Service Cards', 'services-section-elementor'),
                'tab' => \Elementor\Controls_Manager::TAB_CONTENT,
            ]
        );

        // Service 1
        $this->add_control(
            'service_1_heading',
            [
                'label' => esc_html__('Service 1', 'services-section-elementor'),
                'type' => \Elementor\Controls_Manager::HEADING,
                'separator' => 'before',
            ]
        );

        $this->add_control(
            'service_1_image',
            [
                'label' => esc_html__('Background Image', 'services-section-elementor'),
                'type' => \Elementor\Controls_Manager::MEDIA,
                'default' => [
                    'url' => 'https://images.unsplash.com/photo-1587440871875-191322ee64b0?q=80&w=2071&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
                ],
            ]
        );

        $this->add_control(
            'service_1_title',
            [
                'label' => esc_html__('Title', 'services-section-elementor'),
                'type' => \Elementor\Controls_Manager::TEXTAREA,
                'default' => esc_html__('uI/uX creative design', 'services-section-elementor'),
                'placeholder' => esc_html__('Enter service title', 'services-section-elementor'),
                'rows' => 2,
            ]
        );

        $this->add_control(
            'service_1_description',
            [
                'label' => esc_html__('Description', 'services-section-elementor'),
                'type' => \Elementor\Controls_Manager::TEXTAREA,
                'default' => esc_html__('Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Pellentesque habitant morbi tristique senectus et netus et malesuada fames.', 'services-section-elementor'),
                'placeholder' => esc_html__('Enter service description', 'services-section-elementor'),
                'rows' => 3,
            ]
        );

        // Service 2
        $this->add_control(
            'service_2_heading',
            [
                'label' => esc_html__('Service 2', 'services-section-elementor'),
                'type' => \Elementor\Controls_Manager::HEADING,
                'separator' => 'before',
            ]
        );

        $this->add_control(
            'service_2_image',
            [
                'label' => esc_html__('Background Image', 'services-section-elementor'),
                'type' => \Elementor\Controls_Manager::MEDIA,
                'default' => [
                    'url' => 'https://images.unsplash.com/photo-1499951360447-b19be8fe80f5?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
                ],
            ]
        );

        $this->add_control(
            'service_2_title',
            [
                'label' => esc_html__('Title', 'services-section-elementor'),
                'type' => \Elementor\Controls_Manager::TEXTAREA,
                'default' => esc_html__('visual graphic design', 'services-section-elementor'),
                'placeholder' => esc_html__('Enter service title', 'services-section-elementor'),
                'rows' => 2,
            ]
        );

        $this->add_control(
            'service_2_description',
            [
                'label' => esc_html__('Description', 'services-section-elementor'),
                'type' => \Elementor\Controls_Manager::TEXTAREA,
                'default' => esc_html__('Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Pellentesque habitant morbi tristique senectus et netus et malesuada fames.', 'services-section-elementor'),
                'placeholder' => esc_html__('Enter service description', 'services-section-elementor'),
                'rows' => 3,
            ]
        );

        // Service 3
        $this->add_control(
            'service_3_heading',
            [
                'label' => esc_html__('Service 3', 'services-section-elementor'),
                'type' => \Elementor\Controls_Manager::HEADING,
                'separator' => 'before',
            ]
        );

        $this->add_control(
            'service_3_image',
            [
                'label' => esc_html__('Background Image', 'services-section-elementor'),
                'type' => \Elementor\Controls_Manager::MEDIA,
                'default' => [
                    'url' => 'https://images.unsplash.com/photo-1557804506-669a67965ba0?q=80&w=1974&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
                ],
            ]
        );

        $this->add_control(
            'service_3_title',
            [
                'label' => esc_html__('Title', 'services-section-elementor'),
                'type' => \Elementor\Controls_Manager::TEXTAREA,
                'default' => esc_html__('strategy & digital marketing', 'services-section-elementor'),
                'placeholder' => esc_html__('Enter service title', 'services-section-elementor'),
                'rows' => 2,
            ]
        );

        $this->add_control(
            'service_3_description',
            [
                'label' => esc_html__('Description', 'services-section-elementor'),
                'type' => \Elementor\Controls_Manager::TEXTAREA,
                'default' => esc_html__('Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Pellentesque habitant morbi tristique senectus et netus et malesuada fames.', 'services-section-elementor'),
                'placeholder' => esc_html__('Enter service description', 'services-section-elementor'),
                'rows' => 3,
            ]
        );

        // Service 4
        $this->add_control(
            'service_4_heading',
            [
                'label' => esc_html__('Service 4', 'services-section-elementor'),
                'type' => \Elementor\Controls_Manager::HEADING,
                'separator' => 'before',
            ]
        );

        $this->add_control(
            'service_4_image',
            [
                'label' => esc_html__('Background Image', 'services-section-elementor'),
                'type' => \Elementor\Controls_Manager::MEDIA,
                'default' => [
                    'url' => 'https://images.unsplash.com/photo-1600880292203-757bb62b4baf?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
                ],
            ]
        );

        $this->add_control(
            'service_4_title',
            [
                'label' => esc_html__('Title', 'services-section-elementor'),
                'type' => \Elementor\Controls_Manager::TEXTAREA,
                'default' => esc_html__('effective business growth', 'services-section-elementor'),
                'placeholder' => esc_html__('Enter service title', 'services-section-elementor'),
                'rows' => 2,
            ]
        );

        $this->add_control(
            'service_4_description',
            [
                'label' => esc_html__('Description', 'services-section-elementor'),
                'type' => \Elementor\Controls_Manager::TEXTAREA,
                'default' => esc_html__('Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Pellentesque habitant morbi tristique senectus et netus et malesuada fames.', 'services-section-elementor'),
                'placeholder' => esc_html__('Enter service description', 'services-section-elementor'),
                'rows' => 3,
            ]
        );

        $this->end_controls_section();

        // Style Tab - Colors
        $this->start_controls_section(
            'style_colors_section',
            [
                'label' => esc_html__('Colors', 'services-section-elementor'),
                'tab' => \Elementor\Controls_Manager::TAB_STYLE,
            ]
        );

        $this->add_control(
            'background_color',
            [
                'label' => esc_html__('Background Color', 'services-section-elementor'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#111827',
                'selectors' => [
                    '{{WRAPPER}} .services-section-commandnet' => 'background-color: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'subtitle_color',
            [
                'label' => esc_html__('Subtitle Color', 'services-section-elementor'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#9ca3af',
                'selectors' => [
                    '{{WRAPPER}} .services-subtitle-commandnet' => 'color: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'title_color',
            [
                'label' => esc_html__('Title Color', 'services-section-elementor'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#ffffff',
                'selectors' => [
                    '{{WRAPPER}} .services-title-commandnet' => 'color: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'card_background_color',
            [
                'label' => esc_html__('Card Background Color', 'services-section-elementor'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#1f2937',
                'selectors' => [
                    '{{WRAPPER}} .card-commandnet' => 'background-color: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'card_title_color',
            [
                'label' => esc_html__('Card Title Color', 'services-section-elementor'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#ffffff',
                'selectors' => [
                    '{{WRAPPER}} .card-title-commandnet' => 'color: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'card_description_color',
            [
                'label' => esc_html__('Card Description Color', 'services-section-elementor'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#9ca3af',
                'selectors' => [
                    '{{WRAPPER}} .card-description-commandnet' => 'color: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'hover_overlay_color',
            [
                'label' => esc_html__('Hover Overlay Color', 'services-section-elementor'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#4f46e5',
                'selectors' => [
                    '{{WRAPPER}} .card-commandnet::before' => 'background-color: {{VALUE}};',
                ],
            ]
        );

        $this->end_controls_section();
    }

    /**
     * Render widget output on the frontend.
     */
    protected function render() {
        $settings = $this->get_settings_for_display();

        // Prepare service data
        $services = [
            [
                'image' => $settings['service_1_image']['url'] ?? '',
                'title' => $settings['service_1_title'] ?? '',
                'description' => $settings['service_1_description'] ?? '',
                'position' => 'right'
            ],
            [
                'image' => $settings['service_2_image']['url'] ?? '',
                'title' => $settings['service_2_title'] ?? '',
                'description' => $settings['service_2_description'] ?? '',
                'position' => 'left'
            ],
            [
                'image' => $settings['service_3_image']['url'] ?? '',
                'title' => $settings['service_3_title'] ?? '',
                'description' => $settings['service_3_description'] ?? '',
                'position' => 'right'
            ],
            [
                'image' => $settings['service_4_image']['url'] ?? '',
                'title' => $settings['service_4_title'] ?? '',
                'description' => $settings['service_4_description'] ?? '',
                'position' => 'left'
            ]
        ];
        ?>
        <section class="services-section-commandnet">
            <span class="services-subtitle-commandnet">
                <?php echo esc_html($settings['subtitle']); ?>
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="arrow-icon-commandnet">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M17.25 8.25 21 12m0 0-3.75 3.75M21 12H3" />
                </svg>
            </span>

            <h1 class="services-title-commandnet">
                <?php echo wp_kses_post(nl2br($settings['title'])); ?>
            </h1>

            <div class="grid-offer-commandnet">
                <?php foreach ($services as $index => $service) : ?>
                    <div class="card-commandnet" <?php if ($service['image']) echo 'data-bg-image="' . esc_url($service['image']) . '"'; ?>>
                        <div class="circle-commandnet" <?php if ($service['image']) echo 'style="background-image: url(' . esc_url($service['image']) . ');"'; ?>></div>
                        <div class="card-content-commandnet card-content-<?php echo esc_attr($service['position']); ?>-commandnet">
                            <h2 class="card-title-commandnet">
                                <?php echo wp_kses_post(nl2br($service['title'])); ?>
                            </h2>
                            <p class="card-description-commandnet">
                                <?php echo esc_html($service['description']); ?>
                            </p>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </section>
        <?php
    }

    /**
     * Render widget output in the editor.
     */
    protected function content_template() {
        ?>
        <#
        var services = [
            {
                image: settings.service_1_image.url || '',
                title: settings.service_1_title || '',
                description: settings.service_1_description || '',
                position: 'right'
            },
            {
                image: settings.service_2_image.url || '',
                title: settings.service_2_title || '',
                description: settings.service_2_description || '',
                position: 'left'
            },
            {
                image: settings.service_3_image.url || '',
                title: settings.service_3_title || '',
                description: settings.service_3_description || '',
                position: 'right'
            },
            {
                image: settings.service_4_image.url || '',
                title: settings.service_4_title || '',
                description: settings.service_4_description || '',
                position: 'left'
            }
        ];
        #>
        <section class="services-section-commandnet">
            <span class="services-subtitle-commandnet">
                {{{ settings.subtitle }}}
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="arrow-icon-commandnet">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M17.25 8.25 21 12m0 0-3.75 3.75M21 12H3" />
                </svg>
            </span>

            <h1 class="services-title-commandnet">
                {{{ settings.title.replace(/\n/g, '<br>') }}}
            </h1>

            <div class="grid-offer-commandnet">
                <# _.each(services, function(service, index) { #>
                    <div class="card-commandnet" <# if (service.image) { #>data-bg-image="{{{ service.image }}}"<# } #>>
                        <div class="circle-commandnet" <# if (service.image) { #>style="background-image: url({{{ service.image }}});"<# } #>></div>
                        <div class="card-content-commandnet card-content-{{{ service.position }}}-commandnet">
                            <h2 class="card-title-commandnet">
                                {{{ service.title.replace(/\n/g, '<br>') }}}
                            </h2>
                            <p class="card-description-commandnet">
                                {{{ service.description }}}
                            </p>
                        </div>
                    </div>
                <# }); #>
            </div>
        </section>
        <?php
    }
}

// Initialize the plugin
Services_Section_Elementor::instance();
