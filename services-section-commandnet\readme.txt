=== Services Section CommandNet ===
Contributors: yourname
Tags: services, section, responsive, shortcode, business
Requires at least: 5.0
Tested up to: 6.4
Requires PHP: 7.4
Stable tag: 1.0.0
License: GPLv2 or later
License URI: https://www.gnu.org/licenses/gpl-2.0.html

A responsive services section with hover effects and animations for showcasing business services.

== Description ==

Services Section CommandNet is a WordPress plugin that provides a beautiful, responsive services section with hover effects and animations. Perfect for showcasing your business services in an engaging way.

**Features:**

* Responsive design that works on all devices
* Beautiful hover effects with clip-path animations
* Background images for each service card
* Customizable title and subtitle
* Easy to use shortcode
* Translation ready
* No conflicts with other plugins (uses -commandnet namespace)

**Usage:**

Simply use the shortcode `[services_section_commandnet]` in any post, page, or widget to display the services section.

**Shortcode Attributes:**

* `class` - Add custom CSS classes
* `title` - Customize the main title (default: "Services Built Specifically for your Business")
* `subtitle` - Customize the subtitle (default: "what we're offering")

**Example:**
`[services_section_commandnet title="Our Amazing Services" subtitle="what we offer" class="my-custom-class"]`

== Installation ==

1. Upload the plugin files to the `/wp-content/plugins/services-section-commandnet` directory, or install the plugin through the WordPress plugins screen directly.
2. Activate the plugin through the 'Plugins' screen in WordPress
3. Use the shortcode `[services_section_commandnet]` in your posts, pages, or widgets

== Frequently Asked Questions ==

= How do I customize the content of the service cards? =

Currently, the service cards content is fixed. Future versions will include customization options for the service cards content.

= Can I change the background images? =

The background images are currently set to specific Unsplash images. Future versions will include options to customize these images.

= Does this plugin conflict with other CSS frameworks? =

No, all CSS classes use the `-commandnet` suffix to prevent conflicts with other plugins and themes.

== Screenshots ==

1. Services section display on frontend
2. Hover effects demonstration
3. Responsive design on mobile devices

== Changelog ==

= 1.0.0 =
* Initial release
* Responsive services section with hover effects
* Shortcode implementation
* Translation ready
* WordPress coding standards compliance

== Upgrade Notice ==

= 1.0.0 =
Initial release of Services Section CommandNet plugin.
