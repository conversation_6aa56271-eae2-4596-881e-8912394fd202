# Interactive Bubbles Widget - WordPress Plugin

A WordPress widget plugin that creates animated gooey bubbles with mouse-follow effects and full customization options.

## Installation Instructions

### Method 1: Proper Plugin Directory Structure (Recommended)

1. **Create Plugin Directory:**
   - Navigate to your WordPress installation: `wp-content/plugins/`
   - Create a new folder: `interactive-bubbles-widget`

2. **Copy Plugin Files:**
   Copy all these files into the `wp-content/plugins/interactive-bubbles-widget/` directory:
   ```
   interactive-bubbles-widget/
   ├── interactive-bubbles-widget.php
   ├── interactive-bubbles-widget.css
   ├── interactive-bubbles-widget.js
   └── README.md
   ```

3. **Activate Plugin:**
   - Go to WordPress Admin → Plugins
   - Find "Interactive Bubbles Widget" 
   - Click "Activate"

### Method 2: ZIP Installation

1. Create a ZIP file containing all plugin files
2. Go to WordPress Admin → Plugins → Add New → Upload Plugin
3. Upload the ZIP file and activate

## File Structure

```
wp-content/plugins/interactive-bubbles-widget/
├── interactive-bubbles-widget.php    # Main plugin file
├── interactive-bubbles-widget.css    # Styles for bubbles and animations
├── interactive-bubbles-widget.js     # JavaScript for interactive effects
└── README.md                         # This file
```

## Usage

1. **Add Widget:**
   - Go to Appearance → Widgets
   - Find "Interactive Bubbles" widget
   - Drag to desired widget area (sidebar, footer, etc.)

2. **Customize:**
   - Configure title, subtitle, description
   - Choose colors for text and bubbles
   - Select layout (vertical/horizontal)
   - Adjust typography settings

## Features

- ✅ Animated gooey bubble effects
- ✅ Mouse-follow interactive bubble
- ✅ Full color customization
- ✅ Typography controls
- ✅ Layout options
- ✅ Multiple widget instances support
- ✅ Elementor compatible

## Troubleshooting

### Common Issues

**1. Bubbles not appearing:**
- Check browser console for 404 errors
- Verify plugin files are in correct directory
- Ensure plugin is activated

**2. JavaScript errors:**
- Check if `interactive-bubbles-widget.js` exists in plugin directory
- Verify file permissions (should be readable)

**3. CSS not loading:**
- Check if `interactive-bubbles-widget.css` exists in plugin directory
- Clear any caching plugins

### Debug Mode

Enable WordPress debug mode to see detailed error messages:

Add to `wp-config.php`:
```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

Check debug log at: `wp-content/debug.log`

## Requirements

- WordPress 5.0+
- PHP 7.4+
- Modern browser with CSS3 support

## Version History

- **1.4** - Improved WordPress integration, better error handling
- **1.3** - Added title color control, fixed bubble visibility
- **1.2** - Enhanced typography controls
- **1.1** - Initial widget functionality
- **1.0** - Basic bubble effects

## Support

For support and updates, visit: https://commandnet.co
