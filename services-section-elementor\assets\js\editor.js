/**
 * Services Section Elementor Widget - Editor Scripts
 * Version: 1.0.0
 */

(function($) {
    'use strict';

    // Wait for Elementor to be ready
    $(window).on('elementor:init', function() {
        
        // Add custom CSS for better editor experience
        elementor.hooks.addAction('panel/open_editor/widget/services-section-commandnet', function(panel, model, view) {
            // Add custom styling to the editor panel if needed
            console.log('Services Section widget panel opened');
        });

        // Handle live preview updates
        elementor.channels.editor.on('change', function(controlView, elementView) {
            if (elementView.model.get('widgetType') === 'services-section-commandnet') {
                // Trigger re-render for live preview
                elementView.renderHTML();
            }
        });

        // Custom preview handling for image controls
        elementor.hooks.addAction('frontend/element_ready/services-section-commandnet.default', function($scope) {
            // Handle custom background images
            $scope.find('.card-commandnet').each(function(index) {
                var $card = $(this);
                var $circle = $card.find('.circle-commandnet');
                var bgImage = $card.data('bg-image');
                
                if (bgImage) {
                    $circle.css('background-image', 'url(' + bgImage + ')');
                }
            });
        });

    });

    // Helper function for image preview in editor
    window.servicesSection = {
        updateCardBackground: function(cardIndex, imageUrl) {
            var $card = $('.card-commandnet').eq(cardIndex);
            var $circle = $card.find('.circle-commandnet');
            
            if (imageUrl) {
                $circle.css('background-image', 'url(' + imageUrl + ')');
                $card.attr('data-bg-image', imageUrl);
            }
        }
    };

})(jQuery);
