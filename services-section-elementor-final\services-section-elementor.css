/**
 * Services Section Elementor Widget Styles
 * Version: 1.0.0
 */

/* Base styles */
.elementor-widget-services-section-commandnet * {
    box-sizing: border-box;
}

/* Services section container */
.services-section-commandnet {
    min-height: 100vh;
    background-color: #111827;
    text-align: center;
    padding: 5rem 2rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

/* Services subtitle */
.services-subtitle-commandnet {
    color: #9ca3af;
    font-size: 1.125rem;
    max-width: 32rem;
    margin: 0 auto 0.5rem auto;
    text-transform: capitalize;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Arrow icon */
.arrow-icon-commandnet {
    color: #4f46e5;
    margin-left: 0.75rem;
    width: 1.5rem;
    height: 1.5rem;
    flex-shrink: 0;
}

/* Services title */
.services-title-commandnet {
    color: white;
    font-size: 2.25rem;
    font-weight: 600;
    max-width: 48rem;
    margin: 0 auto 4rem auto;
    line-height: 1.375;
}

/* Grid container */
.grid-offer-commandnet {
    text-align: left;
    display: grid;
    gap: 1.25rem;
    max-width: 80rem;
    margin: 0 auto;
    grid-template-columns: 1fr;
}

/* Card base styles */
.card-commandnet {
    background-color: #1f2937;
    padding: 2.5rem;
    position: relative;
    overflow: hidden;
    border-radius: 0;
    transition: box-shadow 0.3s ease;
}

/* Card content positioning */
.card-content-commandnet {
    position: relative;
    z-index: 2;
}

.card-content-right-commandnet {
    padding-right: 0;
}

.card-content-left-commandnet {
    padding-left: 0;
}

/* Card title */
.card-title-commandnet {
    text-transform: capitalize;
    color: white;
    margin-bottom: 1rem;
    font-size: 1.5rem;
    font-family: "Playfair Display", serif;
    font-weight: 400;
    font-style: normal;
    line-height: 1.2;
}

/* Card description */
.card-description-commandnet {
    color: #9ca3af;
    transition: color 0.8s ease;
    line-height: 1.6;
}

/* Card hover effects */
.card-commandnet::before {
    position: absolute;
    content: "";
    width: 100%;
    height: 100%;
    transition: clip-path 0.6s ease;
    z-index: 1;
    background-color: #4f46e5;
    top: 0;
    left: 0;
}

.card-commandnet:hover {
    box-shadow: 0.063rem 0.063rem 1.25rem 0.375rem rgba(0, 0, 0, 0.53);
}

/* Specific clip-path positions for each card */
.card-commandnet:nth-child(1)::before {
    bottom: 0;
    right: 0;
    clip-path: circle(calc(6.25rem + 7.5vw) at 100% 100%);
}

.card-commandnet:nth-child(2)::before {
    bottom: 0;
    left: 0;
    clip-path: circle(calc(6.25rem + 7.5vw) at 0% 100%);
}

.card-commandnet:nth-child(3)::before {
    top: 0;
    right: 0;
    clip-path: circle(calc(6.25rem + 7.5vw) at 100% 0%);
}

.card-commandnet:nth-child(4)::before {
    top: 0;
    left: 0;
    clip-path: circle(calc(6.25rem + 7.5vw) at 0% 0%);
}

/* Hover animation */
.card-commandnet:hover::before {
    clip-path: circle(110vw at 100% 100%);
}

.card-commandnet:hover .card-description-commandnet {
    color: #fff;
}

/* Circle background images */
@media screen and (min-width: 62.5rem) {
    .circle-commandnet {
        position: absolute;
        width: 100%;
        height: 100%;
        z-index: 0;
        top: 0;
        left: 0;
    }
}

.card-commandnet:nth-child(1) .circle-commandnet {
    background: url("https://images.unsplash.com/photo-1587440871875-191322ee64b0?q=80&w=2071&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D") no-repeat 50% 50%/cover;
    bottom: 0;
    right: 0;
    clip-path: circle(calc(6.25rem + 7.5vw) at 100% 100%);
}

.card-commandnet:nth-child(2) .circle-commandnet {
    background: url("https://images.unsplash.com/photo-1499951360447-b19be8fe80f5?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D") no-repeat 50% 50%/cover;
    bottom: 0;
    left: 0;
    clip-path: circle(calc(6.25rem + 7.5vw) at 0% 100%);
}

.card-commandnet:nth-child(3) .circle-commandnet {
    background: url("https://images.unsplash.com/photo-1557804506-669a67965ba0?q=80&w=1974&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D") no-repeat 50% 50%/cover;
    top: 0;
    right: 0;
    clip-path: circle(calc(6.25rem + 7.5vw) at 100% 0%);
}

.card-commandnet:nth-child(4) .circle-commandnet {
    background: url("https://images.unsplash.com/photo-1600880292203-757bb62b4baf?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D") no-repeat 50% 50%/cover;
    top: 0;
    left: 0;
    clip-path: circle(calc(6.25rem + 7.5vw) at 0% 0%);
}

/* Responsive design */
@media (min-width: 640px) {
    .grid-offer-commandnet {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 768px) {
    .services-title-commandnet {
        font-size: 3rem;
    }
}

@media (min-width: 1024px) {
    .card-content-right-commandnet {
        padding-right: 13rem;
    }

    .card-content-left-commandnet {
        padding-left: 12rem;
    }

    .card-commandnet:nth-child(3) .card-content-right-commandnet {
        padding-right: 11rem;
    }
}

@media (min-width: 1280px) {
    .services-section-commandnet {
        padding-left: 0;
        padding-right: 0;
    }

    .services-title-commandnet {
        font-size: 3.75rem;
    }

    .card-title-commandnet {
        font-size: 1.875rem;
    }
}

/* Elementor editor specific styles */
.elementor-editor-active .card-commandnet::before {
    pointer-events: none;
}

/* Custom background image support */
.card-commandnet[data-bg-image] .circle-commandnet {
    background-size: cover !important;
    background-position: center !important;
    background-repeat: no-repeat !important;
}

/* TheGem theme compatibility */
.thegem-body .services-section-commandnet {
    position: relative;
    z-index: 1;
}
